<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

use think\Route;

Route::rule([

    // 微服务接口列表
    // 获取用户信息接口
    'WfwGetUserInfo'                                 => 'wfw/index/WfwGetUserInfo',
    // 获取配置信息接口
    'WfwGetConfigInfo'                               => 'wfw/index/WfwGetConfigInfo',
    // 获取公告访问量接口
    'WfwGetNoticeInfo'                               => 'wfw/index/WfwGetNoticeInfo',
    // 统计每个服务的访问量
    'WfwCollectServiceVisitCount'                    => 'wfw/index/WfwCollectServiceVisitCount',
    // 统计灰度测试服务的访问量
    'WfwCollectGreyServiceVisitCount'                => 'wfw/index/WfwCollectGreyServiceVisitCount',
    // 微服务我的消息
    'WfwMyMessage'                                   => 'wfw/index/WfwMyMessage',
    // 灵感小站提交建议接口
    'WfwSubmitIdeas'                                 => 'wfw/ideas/WfwSubmitIdeas',
    // 灵感小站回复接口
    'WfwReplyIdeas'                                  => 'wfw/ideas/WfwReplyIdeas',
    // 用户获取自己的建议列表
    'WfwGetUserIdeas'                                => 'wfw/ideas/WfwGetUserIdeas',
    // 老师获取待回复的建议列表
    'WfwGetPendingApproveIdeas'                      => 'wfw/ideas/WfwGetPendingApproveIdeas',
    // 微服务智能推荐
    "WfwAIService"                                   => 'wfw/index/WfwAIService',
    // 微服务灵动小组件 - 获取考试安排
    "WfwGetWidgetStudExamSchedule"                         => 'wfw/index/WfwGetWidgetStudExamSchedule',
    // 微服务灵动小组件 - 获取监考安排
    "WfwGetWidgetTeaExamSchedule"                         => 'wfw/index/WfwGetWidgetTeaExamSchedule',
    // 微服务灵动小组件 - 获取访客预约信息
    "WfwGetWidgetVisitorReservationInfo"                         => 'wfw/index/WfwGetWidgetVisitorReservationInfo',
    // 统一门户接口列表
    // 获取用户信息
    'PortalGetUserInfo'                                => 'wfw/portal/PortalGetUserInfo',
    // 统计每个服务的访问量
    'PortalCollectServiceVisitCount'                   => 'wfw/portal/PortalCollectServiceVisitCount',
    // 获取服务列表
    'PortalGetServicesList'                            => 'wfw/portal/PortalGetServicesList',
    // 获取热门服务
    'PortalGetHotServicesList'                         => 'wfw/portal/PortalGetHotServicesList',
    // 统一门户失物招领小组件
    'PortalGetMySwzlCount'                             => 'wfw/portal/PortalGetMySwzlCount',
    // 统一门户获取通知公告信息
    'PortalGetHenauNotice'                             => 'wfw/portal/PortalGetHenauNotice',

    // 微服务开放接口列表
    // 获取服务列表
    'OpenGetServicesList'                              => 'wfw/open/OpenGetServicesList',
    
    // 信息化办公室资源申请服务
    // 获取用户信息接口
    'GetUserInfo'                                    => 'apply/Itcservice/GetUserInfo',
    // 管理员审批接口
    'ApplyApproval'                                  => 'apply/Itcservice/ApplyApproval',
    // 用户侧显示申请列表
    'UserApplyDataList'                              => 'apply/Itcservice/UserApplyDataList',
    // 管理侧待审批列表
    'PendingApprovalList'                            => 'apply/Itcservice/PendingApprovalList',
    // 管理侧已审批列表
    'ApprovedList'                                   => 'apply/Itcservice/ApprovedList',
    // 用户侧申请记录搜索
    'UserApplyInfoSearch'                            => 'apply/Itcservice/UserApplyInfoSearch',
    // 已审批搜索
    'AdminUserApplySearch'                           => 'apply/Itcservice/AdminUserApplySearch',

    // <电子邮箱申请>
    // 电子邮箱申请提交表单接口
    'EmailApply'                                     => 'apply/Itcservice/EmailApply',

    // <VPN申请>
    // VPN申请提交表单接口
    'VpnApply'                                       => 'apply/Itcservice/VpnApply',

    // <IP申请>
    // IP申请提交表单接口
    'IpAddressApply'                                 => 'apply/Itcservice/IpAddressApply',

    // <服务器申请>
    // 服务器申请提交表单接口
    'ServerApply'                                    => 'apply/Itcservice/ServerApply',

    // <域名申请>
    // 域名申请提交表单接口
    'DomainApply'                                    => 'apply/Itcservice/DomainApply',

    // <无线上网申请>
    // 无线上网申请提交表单接口
    'WIFIApply'                                      => 'apply/Itcservice/WIFIApply',

    // <网络故障报修申请>
    // 网络故障报修申请提交表单接口
    'NetworkRepairApply'                             => 'apply/Itcservice/NetworkRepairApply',

    // <多媒体申请>
    // 多媒体申请申请提交表单接口
    'MultimediaApply'                                => 'apply/Itcservice/MultimediaApply',
    
    
    // MAC地址统计
    "getmac/submit/getuserinfo"                      => 'getmac/Submit/GetUserInfo',
    "getmac/submit/macaddresssubmit"                 => 'getmac/Submit/MacAddressSubmit',


    //IT工作室报名系统
    //获取用户信息接口
    'ItSignUpGetUserInfo'                            => 'itwelcome/signUp/ItSignUpGetUserInfo',
    //提交表单接口
    'ItSignUpSubmitForm'                             => 'itwelcome/signUp/ItSignUpSubmitForm'
]);

return [
    '__pattern__' => [
        'name' => '\w+',
    ],
    '[hello]'     => [
        ':id'   => ['index/hello', ['method' => 'get'], ['id' => '\d+']],
        ':name' => ['index/hello', ['method' => 'post']],
    ],

];


