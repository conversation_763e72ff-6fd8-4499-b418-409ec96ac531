<?php

namespace app\itwelcome\controller;

use think\Request;
use app\itwelcome\model\TableItStudioSign;
use app\itwelcome\model\TableItUserVisit;

class SignUp extends Base
{
    // 获取用户身份信息
    public function ItSignUpGetUserInfo()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // 从 GET 请求中获取 code 参数
        $code = input('get.code');

        // 对于未传递 code 参数的请求进行拦截
        if (!$code) {
            Error('非法请求！');
        }

        // 通过传入的 code 参数，调用 httpGet 函数获取 access_token
        $data = json_decode(httpGet(config('GET_ACCESS_TOKEN_API') . '?appid=' . config('AppID') . '&secret=' . config('AppSecret') . '&code=' . $code . '&grant_type=authorization_code'));

        // 判断是否成功获取 access_token
        if (!$data || $data->status !== 'success') {
            Error($data->data);
        }

        // 从对象中获取 access_token 和 henau_openid
        $data = $data->data;
        $access_token = $data->access_token;
        $henau_openid = $data->henau_openid;

        // 通过 access_token 和 henau_openid 换取用户身份信息
        $user_info = json_decode(httpGet(config('GET_USER_INFO_API') . '?access_token=' . $access_token . '&henau_openid=' . $henau_openid));

        // 如果请求返回为空或请求状态不为 'success' 则抛出异常
        if (!$user_info || $user_info->status !== 'success') {
            Error($data->data);
        }

        // 从获取的用户身份信息中获取用户的学号、姓名、学院
        $user_data = $user_info->data;
        $user_number = $user_data->user_number;
        $user_name = $user_data->user_name;
        $user_section = $user_data->user_section;
        $user_phone = $user_data->user_phone;
        $user_status = $user_data->user_status;
        
        // 记录用户访问信息到数据库
        $visitData = [
            'visit_user_name' => $user_name,
            'visit_user_number' => $user_number,
            'visit_user_section' => $user_section,
            'visit_user_phone' => $user_phone,
            'visit_user_henau_openid' => $henau_openid,
            'visit_ip' => $trueIp,
            'visit_time' => date("Y-m-d H:i:s", time()),
            'visit_ua' => $ua,
        ];

        // 保存访问信息到用户访问表
        $userVisit = new TableItUserVisit($visitData);
        $userVisit->save();

        $res['user_name'] = $user_name;
        $res['user_number'] = $user_number;
        $res['user_section'] = $user_section;
        $res['user_phone'] = $user_phone;
        $res['user_henau_openid'] = $henau_openid;

        // 创建 JWT Token
        $token = $this->CreatJwtToken($res);

        // 返回成功响应
        $res_data = [
            'token' => $token,
            'user_name' => $user_name,
            'user_number' => $user_number,
            'user_section' => $user_section,
            'user_phone' => $user_phone,
            'user_status' => $user_status,
        ];

        PostResSuccess($res_data);
    }

    // 接收表单数据并存储到数据库
    public function ItSignUpSubmitForm()
    {
        // 判断是否是 POST 请求
        if (!Request::instance()->isPost()) {
            throw new \Exception('非法请求！', 403); // 抛出异常，设置状态码为400
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 限定单次报名次数
        $submit_count = count(TableItStudioSign::field('stu_number')->where('sign_time','> time',config('SIGN_UP_DATA_SELECT_TIME'))->where('stu_number',$user_info->user_number)->select());
        if ($submit_count >= 2)
            Error("本次招新每人共计两次报名机会，且以最后一次报名为准！");

        // 使用 input 函数获取表单数据
        $user_sex                        = input('post.sex');
        $user_identity                   = input('post.identity');
        $user_campus                     = input('post.campus');
        $user_major                      = input('post.major');
        $user_qq_number                  = input('post.qq_number');
        $user_first_dirction             = input('post.first_dirction');
        $user_second_dirction            = input('post.second_dirction');
        $user_speciality                 = input('post.speciality');
        $user_experience                 = input('post.experience');

        // 防御违规数据（name字段篡改为数组）请求攻击
        if(is_array($user_major) || is_array($user_sex) || is_array($user_qq_number ) || is_array($user_identity)  || is_array($user_first_dirction)  || is_array($user_second_dirction)  || is_array($user_speciality)  || is_array($user_experience)) {
            Error("非法请求");
        }
        if (!IsMajor($user_major)) {
            Error('专业名称错误,需填写小于30字的专业名称');
        }
        // QQ号判断
        if (!CheckUserContact($user_qq_number)) {
            Error('请填写正确的QQ号!');
        }
        // 爱好特长判断
        if (IsTxt($user_speciality) == 1) {
            Error('爱好特长不能为空！');
        }
        // 爱好特长判断
        if (IsTxt($user_speciality) == 2) {
            Error('爱好特长需小于300字！');
        }
        // 任职经历为空下的处理
        if (IsTxt($user_experience) == 1) {
            Error('任职经历不能为空！');
        }
        // 任职经历长度判断
        if (IsTxt($user_experience) == 2) {
            Error('任职经历需小于600字！');
        }
        // 任职经历、爱好特长、专业全称表情包判断
        if (IsHaveEmoji($user_experience) || IsHaveEmoji($user_speciality) || IsHaveEmoji($user_major)) {
            Error('暂不支持输入emoji表情！');
        }
        
        // 将数据存储到数据库
        $TableItStudioSign = new TableItStudioSign([
            'campus'                    => $user_campus,
            'sign_time'                 => date('Y-m-d H:i:s', time()),
            'college'                   => $user_info->user_section,  // 用户所属学院信息
            'major'                     => $user_major,
            'name'                      => $user_info->user_name,  // 用户姓名信息
            'stu_number'                => $user_info->user_number,  // 用户学号信息
            'henau_openid'              => $user_info->user_henau_openid,
            'sex'                       => $user_sex,
            'phone'                     => $user_info->user_phone,
            'qq_number'                 => $user_qq_number,
            'identity'                  => $user_identity,
            'first_dirction'            => $user_first_dirction,
            'second_dirction'           => $user_second_dirction,
            'speciality'                => $user_speciality,
            'experience'                => $user_experience,
            'sign_ua'                   => Request::instance()->header('user-agent'),
            'sign_ip'                   => GetReverseProxyIP()
        ]);
        // 存入数据库
        $res = $TableItStudioSign->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        PostResSuccess("报名成功！");
    }
}