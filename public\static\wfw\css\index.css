* {
  margin: 0;
  padding: 0;
  border: 0;
}

html {
  height: 100%;
  min-height: 100%;
  position: relative;
  margin: 0;
  padding: 0;
  background-color: #fff;
}

body {
  overflow: auto;
  height: 100%;
  min-height: 100%;
  background: #fff;
  position: relative;
  margin: 0;
  padding: 0;
}

/* 每块模块服务的style */
.content {
  position: relative;
  /* top:160px; */
  flex: 1;
  margin-bottom: 15px;
}

/* 每块服务的flex布局方式 */
.flex-item {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: space-between;
  position: relative;
  padding: 0 15px;
  line-height: 24px;
  justify-content: flex-start;
}

.jump_box {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  /* left: 50%; */
  bottom: 0;
  /* transform: translateX(-50%); */
  width: 100%;
  /* max-width: 500px; */
  background-color: #fff;
  padding: 10px;
}

li {
  height: 40px;
  line-height: 40px;
  text-align: center;
}
.black_mask {
  display: none;
  position: fixed;
  top:0;
  left: 0;
  width: 100%;
  height: 1200px;
  background-color: #4c4c4c4c;
}

/* 每个服务的宽度以及其他样式 */
.wfw-item {
  cursor: pointer;
  width: 85px;
  height: 85px;
}

@keyframes message {
  20% {
    font-size: 40px;
  }
}

/* 媒介布局解决自适应问题 */
@media screen and (max-width: 319px) {
  .wfw-item {
    cursor: pointer;
    width: 71px;
    height: 85px;
  }
}

@media screen and (min-width: 320px) and (max-width: 340px) {
  .wfw-item {
    cursor: pointer;
    width: 75px;
    height: 85px;
  }
}

@media screen and (min-width: 341px) and (max-width: 359px) {
  .wfw-item {
    cursor: pointer;
    width: 80px;
    height: 85px;
  }
}

@media screen and (min-width: 360px) and (max-width: 379px) {
  .wfw-item {
    cursor: pointer;
    width: 85px;
    height: 85px;
  }
}

@media screen and (min-width: 380px) and (max-width: 392px) {
  .wfw-item {
    cursor: pointer;
    width: 90px;
    height: 85px;
  }
}

@media screen and (min-width: 393px) {
  .wfw-item {
    cursor: pointer;
    width: 93px !important;
    height: 85px !important;
  }
}

/* 服务icon以及文字居中 */
.item {
  text-align: center;
  margin-top: 10px;
}

.p {
  display: inline-block;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 背景图片 */
.title {
  /* position: absolute; */
  width: 100%;
  height: 160px;
  background-image: url(/static/wfw/img/bg8.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  font-family: "hwls";
  font-size: 24px;
}

@font-face {
  font-family: "hwls";
  font-display: swap;
  src: url("/static/wfw/font/hwls.ttf") format("truetype");
}

.content-title {
  margin-top: 10px;
}

.content-header {
  background-color: #fff;
}

img {
  margin: 10px 0;
}

/* 通知全局 */
.notice {
  margin-top: 20px;
}

.layui-card-header {
  border-bottom: 1px solid #e3e3e3;
}

/* 图标 */
.layui-icon-app {
  color: #0d9bdc;
}

.icon-tip {
  color: #d44e2d;
}

.item-icon-line1 {
  font-size: 35px;
  margin: 10px 0;
}

.item-icon-line2 {
  font-size: 35px;
  margin: 10px 0;
}

.wfw-header {
  height: 35px;
  line-height: 35px;
}

/* 微服务标题栏边框 */
.wfw-header {
  margin-bottom: 5px;
}

/* 公告主题 */
.notice-body {
  display: flex;
  cursor: pointer;
}

/* 公告内容 */
.notice-content {
  display: flex;
  margin: 0 12px;
  line-height: 36px;
  border-bottom: 1px solid #f5f5f5;
  flex: 1;
}

/* 公告标题 */
.notice-title {
  display: inline-flex;
  font-size: 14px;
}

/* 公告日期 */
.notice-date {
  display: inline-flex;
  flex: 1;
  justify-content: flex-end;
  font-size: 14px;
  color: #7b7b7b;
}

p {
  font-size: 4vm;
}

/* 版本号 */
.releave_div {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 15px;
  color: #999999;
}

/* 修改通知字体大小 */
.font_size_down {
  font-size: small;
}

/* 公告点开更多 */
.notice-header span {
  float: right;
  margin-right: 20px;
}

.notice-header .fa-angle-right {
  position: absolute;
  right: 20px;
  height: 33px;
  line-height: 33px;
  font-size: 25px;
}

/* 悬浮出现小手 */
.point {
  cursor: pointer;
}

/* 弹出层背景颜色 */
.layui-layer-hui .layui-layer-content {
  background-color: #3e3f42;
}

/* 媒介布局调整屏幕最大325px的时候字体大小等 */
@media screen and (max-width: 361px) {
  .font_size_down {
    font-size: x-small;
  }
}

/* 媒介布局解决高宽度情况下的布局问题 */
@media screen and (min-width: 450px) and (max-width: 1150px) {
  .flex-item {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: space-between;
    position: relative;
    padding: 0 15px;
    line-height: 24px;
    margin-left: 20px;
  }

  .wfw-item {
    cursor: pointer;
    width: 85px;
    height: 85px;
    margin: 2px;
  }

  .content {
    position: relative;
    /* top:160px; */
    flex: 1;
    margin-bottom: 35px;
  }
}

/* 媒介布局解决高宽度情况下的背景图片的显示 */
@media screen and (min-width: 1150px) {
  .flex-item {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: space-between;
    position: relative;
    padding: 0 15px;
    line-height: 24px;
    margin-left: 20px;
  }

  .wfw-item {
    cursor: pointer;
    width: 85px;
    height: 85px;
    margin: 2px;
  }

  .content {
    position: relative;
    /* top:160px; */
    flex: 1;
    margin-bottom: 35px;
  }

  .title {
    display: none;
  }

  .content-title {
    margin-top: 0 !important;
  }
}