
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>星座配对</title>
<meta name="description" content="">
<meta http-equiv="X-UA-Compatible" content="chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"> 
<meta name="apple-mobile-web-app-status-bar-style" content="black"> 
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="format-detection" content="telephone=no">
<style>
*{margin: 0; padding: 0;}
.anim-box{ position: relative; width: 80%; height: 500px; margin: 50px auto; background-color: #000; color: #000;}
.anim-box>*{position: absolute;}
.anim-box span{width: 80px; height: 80px; line-height: 80px; text-align: center; border-radius: 100%; background-color: #fff; color: #000; z-index: 999;}
.anim-box span{-webkit-transition: .8s; transition: .8s;}
.anim-box ul{ bottom: 50px; left: 20px; font-size: 0;}
.anim-box ul li{display: inline-block;  width: 80px; height: 80px; line-height: 80px; margin: 20px 20px 0 0;  text-align: center; border-radius: 100%; background-color: #c00; color: #fff; font-size: 14px;}

@-webkit-keyframes beam{
  0%{
    box-shadow:0 0 0px rgba(255,255,255,0);
    opacity: 0.3;
  }
  50%{
    box-shadow: 0 0 100px rgba(255,255,255, 1);
    opacity: 1;
  }
  100%{
    box-shadow:0 0 0px rgba(255,255,255,0);
    opacity: 0.3;
    background-color: #fff;
  }
}
@keyframes beam{
  0%{
    box-shadow:0 0 0px rgba(255,255,255, 0);
    opacity: 0.3;
  }
  50%{
    box-shadow:0 0 100px rgba(255,255,255, 1);
    opacity: 1;
  }
  100%{
    box-shadow:0 0 0px 0px rgba(255,255,255, 0);
    opacity: 0.3;
    background-color: #fff;
  }
}
.beam{animation: beam 1s infinite ; -webkit-animation: beam 1s infinite ; }
</style>
</head>
<body>

<section class="anim-box">
  <span id="me">你</span>
  <ul class="xingzuo">
    <li>天秤座</li>
    <li>处女座</li>
    <li>水瓶座</li>
    <li>双子座</li>
    <li>双鱼座</li>
    <li>白羊座</li>
  </ul>
</section>


<script>
;!function(){
  
  
  var find = function(index){
    var me = document.getElementById('me');
    var ul = document.querySelectorAll('.xingzuo')[0];
    var xzs = ul.querySelectorAll('li');
    
    var x = xzs[index].offsetLeft + ul.offsetLeft;
    var y = xzs[index].offsetTop + ul.offsetTop;
    
    me.style['-webkit-transform'] = 'translate3d('+ x +'px, '+ y +'px, 0px) scale(0.5)';
    me.style['transform'] = 'translate3d('+ x +'px, '+ y +'px, 0px) scale(0.5)';
    me.style.opacity = 0.5;
    
    setTimeout(function(){
      xzs[index].className = 'beam';
      setTimeout(function(){
        me.removeAttribute('style');
      }, 500);
    }, 800);
  };
  
  
  
  setTimeout(function(){
    find(Math.random()*5|0) //模拟匹配
  }, 1000);
  
  
}();
</script>
</body>
</html>