-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-26 15:27:23
-- 服务器版本： 5.7.34-log
-- PHP 版本： 7.3.31

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_info`
--

CREATE TABLE `table_info` (
  `user_id` bigint(20) NOT NULL,
  `user_number` varchar(20) NOT NULL,
  `user_name` varchar(10) DEFAULT NULL,
  `user_section` varchar(50) DEFAULT NULL,
  `user_henau_openid` varchar(255) DEFAULT NULL,
  `user_phone` varchar(15) NOT NULL,
  `user_ideas` text NOT NULL,
  `user_submit_time` datetime DEFAULT NULL,
  `user_write_time` varchar(30) DEFAULT NULL,
  `user_ip` varchar(100) DEFAULT NULL,
  `user_ua` varchar(700) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转储表的索引
--

--
-- 表的索引 `table_info`
--
ALTER TABLE `table_info`
  ADD PRIMARY KEY (`user_id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_info`
--
ALTER TABLE `table_info`
  MODIFY `user_id` bigint(20) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
