<?php

namespace org\wechat;


class JSSDK {
    private $appId;
    private $appSecret;
    private $path;
    private $webUrl;

    public function __construct($appId, $appSecret,$webUrl) {
        $this->appId = $appId;
        $this->appSecret = $appSecret;
        $this->path = __DIR__ . DS;
        $this->webUrl = $webUrl;
        // $this->url = $url;
    }

    public function getSignPackage() {
        $jsapiTicket = $this->getJsApiTicket();
        $url = $this->webUrl;
        $timestamp = time();
        $nonceStr = $this->createNonceStr();

        // 这里参数的顺序要按照 key 值 ASCII 码升序排序
        $string = "jsapi_ticket=$jsapiTicket&noncestr=$nonceStr&timestamp=$timestamp&url=$url";

        $signature = sha1($string);

        $signPackage = array(
            "appId"     => $this->appId,
            "nonceStr"  => $nonceStr,
            "timestamp" => $timestamp,
            "url"       => $url,
            "signature" => $signature,
            "rawString" => $string
        );
        return $signPackage;
    }

    private function createNonceStr($length = 16) {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }

    private function getJsApiTicket() {
        // jsapi_ticket 应该全局存储与更新，以下代码以写入到文件中做示例
        // $data = json_decode($this->get_php_file("jsapi_ticket.php"));
        $data = json_decode(file_get_contents($this->path . 'jsapi_ticket.txt'));
        if ($data->expire_time < time()) {
            $accessToken = $this->getAccessToken();
            // 如果是企业号用以下 URL 获取 ticket
            // $url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=$accessToken";
            $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi&access_token=$accessToken";
            $res = json_decode($this->httpGet($url));
            $ticket = $res->ticket;
            if ($ticket) {
                $data->expire_time = time() + 7000;
                $data->jsapi_ticket = $ticket;
                $this->set_php_file($this->path . "jsapi_ticket.txt", $data );
            }
        } else {
            $ticket = $data->jsapi_ticket;
        }

        return $ticket;
    }

    private function getAccessToken() {
        // access_token 应该全局存储与更新，以下代码以写入到文件中做示例
        // $data = json_decode($this->get_php_file("access_token.php"));
        $data = json_decode(file_get_contents($this->path . "access_token.txt"));
        if ($data->expire_time < time()) {
            // 如果是企业号用以下URL获取access_token
            // $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=$this->appId&corpsecret=$this->appSecret";
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$this->appId&secret=$this->appSecret";
            $res = json_decode($this->httpGet($url));
            $access_token = $res->access_token;
            if ($access_token) {
                $data->expire_time = time() + 7000;
                $data->access_token = $access_token;
                $this->set_php_file($this->path . "access_token.txt", $data);
            }
        } else {
            $access_token = $data->access_token;
        }
        return $access_token;
    }

    private function httpGet($url) {
      $curl = curl_init();

      // 指定url
      curl_setopt($curl, CURLOPT_URL, $url);
      // 读取的信息以文件流的形式返回
      curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
      // 不返回HTTP头部信息
      curl_setopt($curl, CURLOPT_HEADER, false);

      // 关闭ssl验证
      curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

      // 强制使用ipv4解析
      curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);

      $str = curl_exec($curl);
      //echo $str."<br/>";
      curl_close($curl);
      return $str;
        
    }

    private function get_php_file($filename) {
        return trim(substr(file_get_contents($this->path.$filename), 15));
    }
    private function set_php_file($filename, $content) {
        file_put_contents($filename, json_encode($content));

        // $fp = fopen($filename, "w+");
        // fwrite($fp,$content);
        // fclose($fp);
    }
}

