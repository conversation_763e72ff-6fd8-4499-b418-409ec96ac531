-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-28 22:29:01
-- 服务器版本： 5.7.43-log
-- PHP 版本： 7.3.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_ideas_template_message_log`
--

CREATE TABLE `table_ideas_template_message_log` (
  `template_message_id` bigint(20) NOT NULL,
  `template_message_send_time` datetime DEFAULT NULL COMMENT '模板消息发送时间',
  `template_message_sender_ip` varchar(200) DEFAULT NULL,
  `template_message_sender_ua` varchar(700) DEFAULT NULL,
  `template_message_sender_name` varchar(20) DEFAULT NULL COMMENT '模板消息发送者学工号',
  `template_message_sender_number` varchar(20) DEFAULT NULL,
  `template_message_sender_section` varchar(20) DEFAULT NULL,
  `template_message_sender_henau_openid` varchar(50) DEFAULT NULL,
  `template_message_receiver_name` varchar(20) DEFAULT NULL,
  `template_message_receiver_number` varchar(20) DEFAULT NULL COMMENT '模板消息接收者学工号',
  `template_message_receiver_section` varchar(20) DEFAULT NULL,
  `template_message_receiver_henau_openid` varchar(50) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转储表的索引
--

--
-- 表的索引 `table_ideas_template_message_log`
--
ALTER TABLE `table_ideas_template_message_log`
  ADD PRIMARY KEY (`template_message_id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_ideas_template_message_log`
--
ALTER TABLE `table_ideas_template_message_log`
  MODIFY `template_message_id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=68;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
