<?php

namespace app\wfw\controller;

use think\Request;
use app\wfw\model\Info;
use app\wfw\model\IdeasUser;
use app\wfw\model\IdeasTemplateMessageLog;

class Ideas extends Base
{
    // 灵感小站提交建议的接口
    public function WfwSubmitIdeas()
    {
        // 判断是否是 POST 请求
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 在请求中取出数据
        $user_phone = input('post.phone');
        $user_ideas_type = input('post.feedbackType');
        $user_ideas = input('post.feedback');
        $file = request()->file('image');

        // 手机号格式核验
        if (!IsPhoneNum($user_phone)) {
            Error('手机号格式错误！');
        }

        // 检验数据是否合法
        if(!CheckId($user_ideas_type)){
            Error('想法或建议类型参数错误！');
        }

        // feedbackType传整数。1代表校园卡相关；2代表校园网相关；3代表多媒体教室相关
        $approve_user =  IdeasUser::get(['user_approve_type' => $user_ideas_type]);
        if (empty($approve_user)) {
            Error('想法或建议类型参数错误！');
        }

        // 想法或建议表情包判断
        if (IsHaveEmoji($user_ideas)) {
            Error('暂不支持输入emoji表情！');
        }
        
        // 限制字数
        if ($res = checkTextLength($user_ideas, config('APPLY_REASON_TEXT_MIN_LENGTH'), config('APPLY_REASON_TEXT_MAX_LENGTH'))) {
            Error("想法或建议" . $res);
        }

        $save_name = null;
        // 如果用户未上传附件
        if ($file) {
            // 如果附件存在
            // 验证文件是否合法
            $validate = $this->validate(
                ['image' => $file],
                ['image' => 'require|file|image|fileSize:' . config('ALLOW_IMAGE_MAX') . '|fileExt:' . config('ALLOW_IMAGE_TYPE')]
            );
            if (true !== $validate) {
                // 验证不通过，返回错误信息
                Error($validate);
            }

            // 移动附件到指定的目录
            $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

            if (!$info)
                Error('请求失败，请稍后重试！');

            // 获取上传文件的网络访问路径
            $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') . '/uploads' . $info->getSaveName());
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 将数据存储到数据库
        $TableIdea = new Info([
            'user_name'                  => $user_info->user_name,
            'user_number'                => $user_info->user_number,
            'user_section'               => $user_info->user_section,
            'user_henau_openid'          => $user_info->user_henau_openid,
            'user_ideas_type'            => $user_ideas_type,
            'user_ideas'                 => $user_ideas,
            'user_phone'                 => $user_phone,
            'user_ideas_attachment'      => $save_name,
            'approve_user_name'          => $approve_user->user_name,
            'approve_user_number'        => $approve_user->user_number,
            'approve_user_section'       => $approve_user->user_section,
            'approve_user_henau_openid'  => $approve_user->user_henau_openid,
            'user_submit_time'           => date('Y-m-d H:i:s', time()),
            'user_ip'                    => $trueIp,
            'user_ua'                    => $ua,
        ]);
        // 存入数据库
        $res = $TableIdea->save();
        if (!$res)
            Error('请求失败，请稍后重试！');

        // 更新数据表数据
        $approve_user->user_approve_counts = $approve_user->user_approve_counts + 1;
        $approve_user->user_last_approve_time = date('Y-m-d H:i:s', time());
        $approve_user->save();

        // 发送模板消息
        $res_data = SendToApproval($approve_user->user_henau_openid, SectionChange($user_info->user_section) . " " . $user_info->user_name, $user_phone);
        if (!isset($res_data->status) || $res_data->status != 'success') {
            ErrorLog("向灵感小站回复人发送通知失败");
        } else {
            // 将数据存储到数据库
            $TableMessage = new IdeasTemplateMessageLog([
                'template_message_send_time'               => date('Y-m-d H:i:s', time()),
                'template_message_sender_ip'               => $trueIp,
                'template_message_sender_ua'               => $ua,
                'template_message_sender_name'             => $user_info->user_name,
                'template_message_sender_number'           => $user_info->user_number,
                'template_message_sender_section'          => $user_info->user_section,
                'template_message_sender_henau_openid'     => $user_info->user_henau_openid,
                'template_message_receiver_name'           => $approve_user->user_name,
                'template_message_receiver_number'         => $approve_user->user_number,
                'template_message_receiver_section'        => $approve_user->user_section,
                'template_message_receiver_henau_openid'   => $approve_user->user_henau_openid,
            ]);
            // 存入数据库
            $res = $TableMessage->save();
            if (!$res)
                ErrorLog('灵感小站模板消息发送存储数据库失败！');
        }

        Success('提交成功！');
    }

    // 灵感小站老师回复建议的接口
    public function WfwReplyIdeas()
    {
        // 判断是否是 POST 请求
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 在请求中取出数据
        $user_id = input('post.user_id');
        $user_reply_content = input('post.user_reply_content');

        // 检查参数是否合法
        if(!CheckId($user_id)){
            Error('参数错误！');
        }

        // 在数据表中获取回复人学工号和henau_openid
        $info = Info::get(['user_id' => $user_id]);
        // 检验参数
        if (empty($info)) {
            Error('参数错误！');
        }
        // 想法或建议表情包判断
        if (IsHaveEmoji($user_reply_content)) {
            Error('暂不支持输入emoji表情！');
        }
        // 判断内容是否满足长度要求
        if ($res = checkTextLength($user_reply_content, config('APPLY_REASON_TEXT_MIN_LENGTH'), config('APPLY_REASON_TEXT_MAX_LENGTH'))) {
            Error("回复内容" . $res);
        }

        $approve_user_number = $info->approve_user_number;
        $user_henau_openid = $info->user_henau_openid;

        if ($approve_user_number != $user_info->user_number) {
            Error('无回复权限！');
        }

        // 将建议设置为已回复并修改对应的回复信息
        Info::where('user_id', $user_id)->update(['has_approved' => 1,'approve_user_reply_content'=> $user_reply_content]);

        // 发送模板消息
        $message_res = SendToAdvise($user_henau_openid, "已回复");
        if (!isset($message_res->status) || $message_res->status != 'success'){
            ErrorLog("向灵感小站建议人发送通知失败");
        } else {
            // 将数据存储到数据库
            $TableMessage = new IdeasTemplateMessageLog([
                'template_message_send_time'               => date('Y-m-d H:i:s', time()),
                'template_message_sender_ip'               => $trueIp,
                'template_message_sender_ua'               => $ua,
                'template_message_sender_name'             => $user_info->user_name,
                'template_message_sender_number'           => $user_info->user_number,
                'template_message_sender_section'          => $user_info->user_section,
                'template_message_sender_henau_openid'     => $user_info->user_henau_openid,
                'template_message_receiver_name'           => $info->user_name,
                'template_message_receiver_number'         => $info->user_number,
                'template_message_receiver_section'        => $info->user_section,
                'template_message_receiver_henau_openid'   => $info->user_henau_openid,
            ]);
            // 存入数据库
            $res = $TableMessage->save();
            if (!$res)
            ErrorLog('灵感小站模板消息发送存储数据库失败！');
        }

        Success("回复成功！");
    }

    // 建议人获取自己的提交记录
    public function WfwGetUserIdeas()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 取出数据
        $apply_list = Info::field('user_id,user_ideas,user_ideas_type,approve_user_reply_content,user_ideas_attachment,user_submit_time,has_approved')
            ->where('user_number', $user_info->user_number)
            ->where('is_deleted', 0)
            ->order('user_submit_time', 'desc')
            ->select();

        PostResSuccess($apply_list);
    }

    // 获取未回复建议接口
    public function WfwGetPendingApproveIdeas()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 判断是否是回复人
        $user = IdeasUser::get(['user_number' => $user_info->user_number]);
        if (!$user) {
            Error("无权限访问！");
        }

        // 取出回复人对应的回复类型
        $user_reply_type = $user->user_approve_type;

        // 查询数据 
        $pending_reply_list = Info::field('user_id,user_name,user_number,user_section,user_phone,user_ideas,user_ideas_attachment')
            ->where('user_ideas_type', $user_reply_type)
            ->where('has_approved', 0)
            ->where('is_deleted', 0)
            ->order('user_submit_time', 'desc')
            ->select();

        PostResSuccess($pending_reply_list);
    }
}
