@media (prefers-color-scheme: dark) {
    /* 背景 */
    html {
        background-color: #111!important;
    }
    body {
        background-color: #111!important;
    }
    /* 文本 */
    p , div, span{
        color: #bfbfbf;
    }
    /* 修改公告首页头部字体颜色 */
    .know-more{
        color: #bfbfbf!important;
        font-weight: bold;
    }
    /* 按钮 */
    .layui-btn {
        color: #bfbfbf!important;
        background-color: #265f56!important;
    }
    img {
        opacity: 0.6;
    }
    .layui-elem-field {
        border-color: #bfbfbf;
    }
    .layui-timeline-axis {
        background-color: #111111;
    }
    .fbtime p {
        color: #6e6e6e!important;
    }
    .fbtime {
        color: #6e6e6e!important;
    }
}
