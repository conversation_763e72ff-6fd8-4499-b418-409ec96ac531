 
 
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
<title>表格操作 - layui</title>

<link rel="stylesheet" href="../src/css/layui.css">

<style>
body{padding: 20px; /*overflow-y: scroll;*/}
</style>
</head>
<body>

<script type="text/html" id="toolbarDemo">
  <div class="layui-btn-container">
    <button class="layui-btn layui-btn-sm" lay-event="getCheckData">获取选中行数据</button>
    <button class="layui-btn layui-btn-sm" lay-event="getCheckLength">获取选中数目</button>
    <button class="layui-btn layui-btn-sm" lay-event="isAll">验证是否全选</button>
    <button class="layui-btn layui-btn-sm" lay-event="reload">重载</button>
  </div>
</script>

<table id="test" lay-filter="test"></table>

<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
  <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<script type="text/html" id="usernameTpl">
  <a href="" class="layui-table-link">{{d.username || ''}}</a>
</script>

<script type="text/html" id="switchTpl">
  <input type="checkbox" name="yyy" lay-skin="switch" lay-text="女|男">
</script>

<script type="text/html" id="cityTpl">
  <select lay-ignore>
    <option value="浙江杭州">浙江杭州</option>
    <option value="江西南昌">江西南昌</option>
    <option value="湖北武汉">湖北武汉</option>
  </select>
</script>

<script type="text/html" id="checkboxTpl">
  <input type="checkbox" name="" title="锁定" checked>
</script>

<script type="text/html" id="LAY_table_tpl_email">
  <span {{# if(!d.activate){ }}style="color:#999"{{# } }}>{{ d.email }}</span>
</script>

<table id="test2" lay-filter="test2"></table>

<div style="display: none1;">
  <table class="layui-table1" lay-data="{width:800, height: 300, url:'json/table/demo2.json', page: true, limit: 6}">
    <thead>
      <tr>
        <th lay-data="{checkbox:true, fixed:'left'}" rowspan="2"></th>
        <th lay-data="{field:'username', width:80}" rowspan="2">联系人</th>
        <th lay-data="{field:'amount', width:120}" rowspan="2">金额</th>
        <th lay-data="{align:'center'}" colspan="3">地址</th>
        <th lay-data="{fixed: 'right', width: 155, align: 'center', toolbar: '#barDemo'}" rowspan="2">操作</th>
      </tr>
      <tr>
        <th lay-data="{field:'province', width:130}">省</th>
        <th lay-data="{field:'city', width:130}">市</th>
        <th lay-data="{field:'zone', width:200}">区</th>
      </tr>
    </thead>
  </table>

  <table class="layui-table" lay-data="{url:'json/table/demo2.json', toolbar: '#toolbarDemo', page: true, limit: 6}">
    <thead>
      <tr>
        <th lay-data="{field:'username'}" rowspan="3">联系人</th>
        <th lay-data="{field:'amount', width:120,hide:1}" rowspan="3">金额</th>
        <th lay-data="{align:'center'}" colspan="5">地址1</th>
        <th lay-data="{align:'center'}" colspan="2">地址2</th>
        <th lay-data="{fixed: 'right', width: 120, align: 'center', toolbar: '#barDemo'}" rowspan="3">操作</th>
      </tr>
      <tr>
        <th lay-data="{field:'province', width:120,hide:1}" rowspan="2">省</th>
        <th lay-data="{field:'city', width:80,hide:1}" rowspan="2">市</th>
        <th lay-data="{align:'center'}" colspan="2">详细</th>
        <th lay-data="{field:'zone'}" rowspan="2">区</th>
        <th lay-data="{field:'province', width:80}" rowspan="2">省</th>
        <th lay-data="{field:'city', width:80}" rowspan="2">市</th>
      </tr>
      <tr>
        <th lay-data="{field:'address', width:120,hide:1}">小区</th>
        <th lay-data="{field:'house', width:150,hide:1}">单元</th>
      </tr>
    </thead>
  </table>

  <table id="demo"></table>


  <div class="layui-btn-group">
    <button class="layui-btn" data-type="parseTable">转化为数据表格</button>
  </div>

  <table class="layui-table" lay-skin="line" lay-filter="parse-table-demo">
    <thead>
      <tr>
        <th lay-data="{checkbox:true}"></th>
        <th lay-data="{field:'username', width:200}">昵称</th>
        <th lay-data="{field:'joinTime', width:150}">加入时间</th>
        <th lay-data="{field:'sign'}">签名</th>
      </tr> 
    </thead>
    <tbody>
      <tr>
        <td></td>
        <td>贤心1</td>
        <td>2016-11-28</td>
        <td>人生就像是一场修行a</td>
      </tr>
      <tr>
        <td></td>
        <td>贤心2</td>
        <td>2016-11-29</td>
        <td>人生就像是一场修行b</td>
      </tr>
      <tr>
        <td></td>
        <td>贤心3</td>
        <td>2016-11-30</td>
        <td>人生就像是一场修行c</td>
      </tr>
    </tbody>
  </table>

  <table class="layui-table" lay-filter="parse-table-demo">
    <thead>
      <tr>
       <td rowspan="2" lay-data="{field:'louceng'}">楼层</td>
       <td colspan="2">1单元</td>
       <td colspan="2">2单元</td>
      </tr>
      <tr>
       <td lay-data="{field:'men1', width:80}">1门</td>
       <td lay-data="{field:'men2', width:80}">2门</td>
       <td lay-data="{field:'men3', width:80}">1门</td>
       <td lay-data="{field:'men4', width:80}">2门</td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td>3楼</td>
       <td>301</td>
       <td>302</td>
       <td>301</td>
       <td>302</td>
      </tr>
      <tr>
       <td>2楼</td>
       <td>201</td>
       <td>202</td>
       <td>201</td>
       <td>202</td>
      </tr>
      <tr>
       <td>1楼</td>
       <td>101</td>
       <td>102</td>
       <td>101</td>
       <td>102</td>
      </tr>
    </tbody>
  </table>
</div>

<script src="../src/layui.js" src="//layui.hcwl520.com.cn/layui-v2.4.5/layui.js" charset="utf-8"></script>
<script>
layui.use('table', function(){
  var table = layui.table;
  //return;
  
  //渲染
  window.ins1 = table.render({
    elem: '#test'
    ,height: 400
    //,width: 600
    ,title: '用户数据表'
    ,url: 'json/table/demo1.json'
    //,size: 'lg'
    ,page: {
      
    }
    
    ,autoSort: false
    //,loading: false
    ,totalRow: true
    ,limit: 30
    ,toolbar: '#toolbarDemo'
    ,defaultToolbar: ['filter', 'exports', 'print', {
      title: '帮助'
      ,layEvent: 'LAYTABLE_TIPS'
      ,icon: 'layui-icon-tips'
    }]
    ,cols: [[
      {type: 'checkbox', fixed: 'left'}
      ,{field:'id', title:'ID', width:80, fixed: 'left', unresize: true, sort: true, totalRowText: '合计：'}
      ,{field:'username', title:'用户名', width:120, edit: 'text', templet: '#usernameTpl'}
      ,{field:'email', title:'邮箱', hide: 0, width:150, edit: 'text', templet: function(d){
        return '<em>'+ d.email +'</em>'
      }}
      ,{field:'sex', title:'性别', width:80, edit: 'text', sort: true}
      ,{field:'city', title:'城市', width:120, templet: '#cityTpl1'}
      ,{field:'sign', title:'签名'}
      ,{field:'experience', title:'积分', width:80, sort: true, totalRow: true, templet: '<div>{{ d.experience }} 分</div>'}
      ,{field:'ip', title:'IP', width:120}
      ,{field:'logins', title:'登入次数', width:100, sort: true, totalRow: true}
      ,{field:'joinTime', title:'加入时间', width:120}
      ,{fixed: 'right', title:'操作', toolbar: '#barDemo', width:150}
    ]]
    
    ,initSort: {
      field: 'experience' //排序字段，对应 cols 设定的各字段名
      ,type: 'desc' //排序方式  asc: 升序、desc: 降序、null: 默认排序
    }

    
    /*
    ,response: {
      statusName: 'status'
      ,statusCode: 200
    }
    ,parseData: function(res){
      return {
        "status": res.status
        ,"msg": res.message
        ,"count": res.total
        ,"data": res.data.list
      };
    }
    */
  });
  
  //工具栏事件
  table.on('toolbar(test)', function(obj){
    var checkStatus = table.checkStatus(obj.config.id);
    switch(obj.event){
      case 'add':
        layer.msg('添加');
      break;
      case 'update':
        layer.msg('编辑');
      break;
      case 'delete':
        layer.msg('删除');
      break;
      case 'getCheckData':
        var data = checkStatus.data;
        layer.alert(JSON.stringify(data));
      break;
      case 'getCheckLength':
        var data = checkStatus.data;
        layer.msg('选中了：'+ data.length + ' 个');
      break;
      case 'isAll':
        layer.msg(checkStatus.isAll ? '全选': '未全选')
      break;
      case 'LAYTABLE_TIPS':
        layer.alert('Table for layui-v'+ layui.v);
      break;
      case 'reload':
        table.reload('test', {
          page: {curr: 5}
          //,height: 300
          //,url: 'x'
        }, 'data');
      break;
    };
  });

  table.on('row(test)', function(obj){
    console.log(obj);
    //layer.closeAll('tips');
  });
  
  
  
  table.render({
    elem: '#test2'
    ,url: 'json/table/demo1.json'
    ,contentType: 'application/json'
    ,page: { //详细参数可参考 laypage 组件文档
      curr: 5
      ,groups: 1
      ,first: false
      ,last: false
      ,layout: ['limit', 'prev', 'page', 'next', 'count'] //自定义分页布局
    }
    //,height: 300
    ,cellMinWidth: 80
    //,skin: 'line'
    ,toolbar: true
    ,cols: [[
      {field: 'id', hide: true}
      ,{field: 'username', title: '用户名'}
      ,{field: 'email', title: '邮箱'}
      ,{title:'操作', align:'center', toolbar: '#barDemo'}
      /*
      {type:'numbers'}
      ,{field:'id', title:'ID', unresize: true, sort: true}
      ,{field:'username', title:'用户名', templet: '#usernameTpl'}
      ,{field:'email', title:'邮箱'}
      ,{xfield:'sex', title:'性别', templet: '#switchTpl', minWidth: 85, align:'center'}
      ,{field:'lock', title:'是否锁定', templet: '#checkboxTpl', minWidth: 110, align:'center'}
      ,{field:'city', title:'城市'}
      */
    ]]
  });

  //监听表格行点击
  table.on('tr', function(obj){
    console.log(obj)
  });

  //监听表格复选框选择
  table.on('checkbox(test)', function(obj){
    console.log(obj)
  });

  //监听表格单选框选择
  table.on('radio(test)', function(obj){
    console.log(obj)
  });
  
  //监听表格单选框选择
  table.on('rowDouble(test)', function(obj){
    console.log(obj);
  });
  
  //监听单元格编辑
  table.on('edit(test)', function(obj){
    var value = obj.value //得到修改后的值
    ,data = obj.data //得到所在行所有键值
    ,field = obj.field; //得到字段
    
    console.log(obj)
  });
  
  //监听行工具事件
  table.on('tool(test)', function(obj){
    var data = obj.data;
    //console.log(obj)
    if(obj.event === 'del'){
      layer.confirm('真的删除行么', function(index){
        obj.del();
        layer.close(index);
      });
    } else if(obj.event === 'edit'){
      layer.prompt({
        formType: 2
        ,value: data.email
      }, function(value, index){
        obj.update({
          email: value
        });
        layer.close(index);
      });
    }
  });
  
  //监听排序
  table.on('sort(test)', function(obj){
    console.log(this)
    
    //return;
    layer.msg('服务端排序。order by '+ obj.field + ' ' + obj.type);
    //服务端排序
    table.reload('test', {
      initSort: obj
      //,page: {curr: 1} //重新从第一页开始
      ,where: { //重新请求服务端
        key: obj.field //排序字段
        ,order: obj.type //排序方式
      }
    });
  });
  
  //return;
  
  //直接赋值数据
  table.render({
    elem: '#demo'
    //,width: 900
    //,height: 274
    ,cols: [[ //标题栏
      {type: 'checkbox', LAY_CHECKED: true}
      ,{field: 'id', title: 'ID', width: 80, sort: true}
      ,{type: 'space', width: 100} //空列
      ,{field: 'username', title: '用户名', width: 120}
      ,{field: 'email', title: '邮箱', width: 150}
      ,{field: 'sign', title: '签名', width: 150}
      ,{field: 'sex', title: '性别', width: 80}
      ,{field: 'city', title: '城市', width: 100}
      ,{field: 'experience', title: '积分', width: 80, sort: true}
    ]]
    ,data: [{
      "id": "10001"
      ,"username": "杜甫"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "116"
      ,"ip": "***********"
      ,"logins": "108"
      ,"joinTime": "2016-10-14"
    }, {
      "id": "10002"
      ,"username": "李白"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "12"
      ,"ip": "***********"
      ,"logins": "106"
      ,"joinTime": "2016-10-14"
      ,"LAY_CHECKED": true
    }, {
      "id": "10003"
      ,"username": "王勃"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "65"
      ,"ip": "***********"
      ,"logins": "106"
      ,"joinTime": "2016-10-14"
    }, {
      "id": "10004"
      ,"username": "贤心"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "666"
      ,"ip": "***********"
      ,"logins": "106"
      ,"joinTime": "2016-10-14"
    }, {
      "id": "10005"
      ,"username": "贤心"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "86"
      ,"ip": "***********"
      ,"logins": "106"
      ,"joinTime": "2016-10-14"
    }, {
      "id": "10006"
      ,"username": "贤心"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "12"
      ,"ip": "***********"
      ,"logins": "106"
      ,"joinTime": "2016-10-14"
    }, {
      "id": "10007"
      ,"username": "贤心"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "16"
      ,"ip": "***********"
      ,"logins": "106"
      ,"joinTime": "2016-10-14"
    }, {
      "id": "10008"
      ,"username": "贤心"
      ,"email": "<EMAIL>"
      ,"sex": "男"
      ,"city": "浙江杭州"
      ,"sign": "人生恰似一场修行"
      ,"experience": "106"
      ,"ip": "***********"
      ,"logins": "106"
      ,"joinTime": "2016-10-14"
    }]

    ,skin: 'row' //表格风格
    ,even: true
    //,size: 'lg' //尺寸
    
    ,page: true //是否显示分页
    ,limits: [3,5,10]
    ,limit: 3 //每页默认显示的数量
    //,loading: false //请求数据时，是否显示loading
  });
  
  
  var $ = layui.jquery, active = {
    parseTable: function(){
      table.init('parse-table-demo', {
        limit: 3
      });
    }
    ,add: function(){
      table.addRow('test')
    }
  };
  $('i').on('click', function(){
    var type = $(this).data('type');
    active[type] ? active[type].call(this) : '';
  });
  $('.layui-btn').on('click', function(){
    var type = $(this).data('type');
    active[type] ? active[type].call(this) : '';
  });
});
</script>
</body>
</html>
