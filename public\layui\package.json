{"name": "layui-src", "realname": "layui", "version": "2.5.7", "independents": {"layim": "3.9.6"}, "description": "Classic modular front-end component library", "main": "dist/layui.js", "license": "MIT", "scripts": {"test": "karma start karma.conf.unit.js", "test:cov": "npm test -- --reporters mocha,coverage", "test:sauce": "karma start karma.conf.sauce.js", "test:watch": "npm test -- --auto-watch --no-single-run"}, "repository": {"type": "git", "url": "git+ssh://**************/sentsin/layui.git"}, "author": ["sentsin <<EMAIL>>"], "homepage": "https://www.layui.com", "devDependencies": {"chai": "^4.1.1", "del": "^2.2.2", "gulp": "^3.9.1", "gulp-concat": "^2.6.0 ", "gulp-header": "^1.8.8", "gulp-if": "^2.0.1", "gulp-minify-css": "^1.2.4", "gulp-rename": "^1.2.2", "gulp-uglify": "^1.5.4", "gulp-zip": "^4.0.0", "karma": "^1.5.0", "karma-chai": "^0.1.0", "karma-chai-sinon": "^0.1.5", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.3", "karma-phantomjs-launcher": "^1.0.4", "karma-sauce-launcher": "^1.1.0", "minimist": "^1.2.0", "mocha": "^3.2.0", "sinon": "^3.2.1", "sinon-chai": "^2.13.0"}, "bugs": {"url": "https://github.com/sentsin/layui/issues"}, "directories": {"doc": "doc", "example": "examples", "test": "test"}, "dependencies": {}, "keywords": ["layui", "ui", "JavaScript Framework", "toolkit", "front-end component library"]}