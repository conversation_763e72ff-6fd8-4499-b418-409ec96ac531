---------------------------------------------------------------
[ 2025-08-16T14:11:45+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.017276s] [吞吐率：57.88req/s] [内存消耗：1,195.72kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
---------------------------------------------------------------
[ 2025-08-16T14:14:36+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.020080s] [吞吐率：49.80req/s] [内存消耗：1,195.70kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
---------------------------------------------------------------
[ 2025-08-16T14:14:58+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.014961s] [吞吐率：66.84req/s] [内存消耗：1,195.70kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
---------------------------------------------------------------
[ 2025-08-16T14:15:03+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.015107s] [吞吐率：66.19req/s] [内存消耗：1,195.70kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
---------------------------------------------------------------
[ 2025-08-16T14:15:29+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.014014s] [吞吐率：71.36req/s] [内存消耗：1,195.70kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
---------------------------------------------------------------
[ 2025-08-16T14:16:36+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.016669s] [吞吐率：59.99req/s] [内存消耗：1,195.70kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
---------------------------------------------------------------
[ 2025-08-16T14:16:55+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.017923s] [吞吐率：55.79req/s] [内存消耗：1,195.70kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
---------------------------------------------------------------
[ 2025-08-16T14:17:35+08:00 ] 127.0.0.1 GET microserviceweb/
[运行时间：0.020804s] [吞吐率：48.07req/s] [内存消耗：1,195.70kb] [文件加载：28]
[ info ] [ LANG ] D:\AA_thing\microservice-dev-pc-ls\thinkphp\lang\zh-cn.php
[ info ] [ ROUTE ] array (
  'type' => 'module',
  'module' => 
  array (
    0 => '',
    1 => NULL,
    2 => NULL,
  ),
)
[ info ] [ HEADER ] array (
  'sec-gpc' => '1',
  'dnt' => '1',
  'accept-language' => 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-US;q=0.6',
  'accept-encoding' => 'gzip, deflate',
  'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
  'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'upgrade-insecure-requests' => '1',
  'connection' => 'close',
  'host' => 'microserviceweb',
)
[ info ] [ PARAM ] array (
)
[ info ] [ LOG ] INIT File
[ expect ] think\exception\HttpException: module not exists:index in D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php:535
Stack trace:
#0 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(456): think\App::module(Array, Array, NULL)
#1 D:\AA_thing\microservice-dev-pc-ls\thinkphp\library\think\App.php(139): think\App::exec(Array, Array)
#2 D:\AA_thing\microservice-dev-pc-ls\thinkphp\start.php(19): think\App::run()
#3 D:\AA_thing\microservice-dev-pc-ls\public\index.php(23): require('D:\\AA_thing\\mic...')
#4 {main}
