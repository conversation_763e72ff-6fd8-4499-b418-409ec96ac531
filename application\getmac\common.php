<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

// 应用公共文件
function RequestLimit($minute, $limit)
{
    // 获取用户的ip
    $real_ip = GetReverseProxyIP();
    $ip_str = md5($real_ip);
    // 获取用户访问的接口路径
    $api_path = request()->url();
    $second = $minute * 60;//分钟转换成时间戳
    // 将ip和访问的接口路径md5加密成一个字符串 ,这样就代表同一个用户访问的接口
    $UC = md5($ip_str . $api_path);
    $request_time = time();
    $request_rate_limit_table = new \app\getmac\model\RequestRateLimit;
    if ($request_rate_limit_table->where('request_api', $UC)->find() == false) {
        $request_rate_limit_table
            ->data([
                'real_ip' => $real_ip,
                'time_stamp' => $request_time,
                'request_time' => date('Y-m-d H:i:s', $request_time),
                'request_api' => $UC,
                'request_num' => 1,
                'request_api_origin' => $api_path,
            ]);
        $request_rate_limit_table->save();
    } else {
        $time_stamp = $request_rate_limit_table->field('time_stamp')->where('request_api', $UC)->find();
        $time_sub = $request_time - intval($time_stamp['time_stamp']);
        //10分钟对应时间戳600000
        if ($time_sub < $second) {
            $request_num_data = $request_rate_limit_table->field('request_num')->where('request_api', $UC)->find();
            if ($request_num_data['request_num'] < $limit) {
                $request_rate_limit_table
                    ->where('request_api', $UC)
                    ->update(['request_num' => $request_num_data['request_num'] + 1]);
            } else {
                $data['code'] = 1;
                $origin_time = $request_rate_limit_table->where('request_api', $UC)->value('time_stamp');
                $time = (int)($minute - (time() - $origin_time) / 60);
                if ($time == 0) {
                    $time = 60 * ($minute - (time() - $origin_time) / 60);
                    $data['msg'] = '请在' . $time . '秒后重试！';
                } else {
                    $data['msg'] = '请在' . ($time + 1) . '分钟后重试！';
                }
                return $data;
            }
        } else {
            $request_rate_limit_table->where('request_api', $UC)->delete();
        }
    }
}