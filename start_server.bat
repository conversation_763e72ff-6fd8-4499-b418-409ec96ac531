@echo off
echo ========================================
echo ‘微服务后端项目 - 快速启动脚本’
echo ========================================
echo.

echo ‘正在检查PHP环境...’
php -v >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] PHP未安装或未添加到PATH
    echo 请确保phpStudy已安装并启动PHP服务
    pause
    exit /b 1
)

echo PHP环境检查通过
echo.

echo 正在启动PHP内置服务器...
echo 服务地址: http://localhost:8888
echo 按 Ctrl+C 停止服务器
echo.

cd /d "%~dp0public"
php -S localhost:8888

pause
