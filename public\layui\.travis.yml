language: node_js
node_js:
  - node
dist: trusty
addons:
  sauce_connect:
    username: layui
  jwt:
    secure: "c+I/AGPj+9cdZOtwGv9eFBdFNlo/BtSEnZ/mGXBLM2+FUn8Dptvhqy1XalRBul3sNiCyv2lNtcZPGw0kirI4EjGiXFfqghq9psvwOchkNM+bFxAiH+uRYCVb1ouDbpAh0w4d/nxpB11fPdVNzudwbiI/ii8LNm1sDDnJOklHiuzWBgOVN2jkzNRapacLfto6bWjnyS4r/zElLwnKpXlN6cIJFzYBU1f/RS68xaHwr/9+wvf5gNzL7OmmiIxl+UJJMejoK3G7I6DTiXyosJxsnljSxG0zbDSDL9lzPeQjFClya25ubbCFPv/UADlVAlz5Y4SLIaTUaRWD7tVphpku5S9XBwIopRQBdtp6y1Ebh/F7pDAiuN8lHzvkr6z++ld9nVcSJppSWN/tyibN7b+C7m6TyreVMHFjw+egAbd3wPVgrD30Vswu9vQXyVydcRJiP295VclgaHobdZKEwrqK7mujsOVOlZEoZWm2B+MLGfvYIjhVdpcRGlAZN+VvA+ea93t/poY1LB1/qH0vF2jMNa3ZI6AWrZaaTbToMUEj6QB9tnOGnGvINOGJDamMMB6aeCLaEgZzRqvTYXgzJAi1kYQKkCL//v3Z6od5eDBUVgMEOkTIchPjSDX5lwMedroPwnFql9ArIWnm7mLHQToEArOoKQhSlFOx+sA/nortCL4="
sudo: false
cache:
  directories:
    - node_modules
script:
  - npm run test:cov
  - npm run test:sauce
after_script:
  - npm install coveralls && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./coverage
