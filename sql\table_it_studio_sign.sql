-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-26 15:27:30
-- 服务器版本： 5.7.34-log
-- PHP 版本： 7.3.31

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_it_studio_sign`
--

CREATE TABLE `table_it_studio_sign` (
  `sign_id` bigint(20) NOT NULL,
  `sign_time` datetime DEFAULT NULL,
  `campus` varchar(10) DEFAULT NULL,
  `college` varchar(30) DEFAULT NULL,
  `major` varchar(50) DEFAULT NULL,
  `name` varchar(10) DEFAULT NULL,
  `stu_number` char(10) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `qq_number` varchar(20) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `identity` varchar(20) DEFAULT NULL,
  `first_dirction` varchar(20) DEFAULT NULL,
  `second_dirction` varchar(20) DEFAULT NULL,
  `speciality` varchar(700) DEFAULT NULL,
  `experience` varchar(700) DEFAULT NULL,
  `sign_ua` varchar(1000) DEFAULT NULL,
  `sign_ip` varchar(100) NOT NULL,
  `photo_save_name` varchar(255) DEFAULT NULL,
  `henau_openid` varchar(64) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转储表的索引
--

--
-- 表的索引 `table_it_studio_sign`
--
ALTER TABLE `table_it_studio_sign`
  ADD PRIMARY KEY (`sign_id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_it_studio_sign`
--
ALTER TABLE `table_it_studio_sign`
  MODIFY `sign_id` bigint(20) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
