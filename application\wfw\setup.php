<?php

return [
    // +----------------------------------------------------------------------
    // | 微服务新版本配置
    // +----------------------------------------------------------------------
    
    // 获取token的接口
    'GET_ACCESS_TOKEN_API'                            => 'https://oauth.henau.edu.cn/oauth2_server/access_token',

    // 获取用户信息接口
    'GET_USER_INFO_API'                               => 'https://oauth.henau.edu.cn/oauth2_server/userinfo',

    //请求时间限制
    'SAVEDATA_REQUEST_LIMIT_TIME'                     => 1,

    //每分钟请求次数
    'SAVEDATA_REQUEST_LIMIT_NUM'                      => 15,

    // 推送给老师回复相关模板消息
    'SEND_TO_APPROVER'                                => "ba6bff75c2b7160f0f10245859844f1c" ,

    // 回复建议人模板消息
    'SEND_TO_ADVISER'                                 => "3bf3c152448369bdbfb0bc2529d940b5" ,

    // 消息返回
    'MESSAGE_TO_APPROVAL_URL'                         => 'https://microservice.leesong.top/henauwfw/#/FeedbackReply',
    'MESSAGE_TO_ADVISER_URL'                          => 'https://microservice.leesong.top/henauwfw/#/MyFeedback',

    // 微信模板消息配置
    'WXMESSAGE_API'                                   => "https://oauth.henau.edu.cn/oauth2_server/message?",

    // 建议原因字数限制
    'APPLY_REASON_TEXT_MIN_LENGTH'                    => 5,
    'APPLY_REASON_TEXT_MAX_LENGTH'                    => 600,

    // 允许上传的图片最大值
    'ALLOW_IMAGE_MAX'                                 => 16777216,
    'ALLOW_IMAGE_TYPE'                                => 'jpg,png,jpeg,gif',

    //上传文件服务器地址
    'SERVER_ADDRESS'                                  => 'https://microservice.leesong.top',

    // 学院 / 部门简称
    'SECTION_ABBREVIATE'   =>  [
        '软件学院'                           => '软件学院',
        '动物科技学院'                       => '动科学院',
        '动物医学院'                         => '动医学院',
        '风景园林与艺术学院'                 => '景艺学院',
        '国际教育学院'                       => '国教学院',
        '机电工程学院'                       => '机电学院',
        '经济与管理学院'                     => '经管学院',
        '理学院'                             => '理学院',
        '林学院'                             => '林学院',
        '农学院'                             => '农学院',
        '生命科学学院'                       => '生命学院',
        '食品科学技术学院'                   => '食品学院',
        '体育学院'                           => '体育学院',
        '外国语学院'                         => '外语学院',
        '文法学院'                           => '文法学院',
        '信息与管理科学学院'                 => '信管学院',
        '许昌校区'                           => '许昌学院',
        '烟草学院'                           => '烟草学院',
        '园艺学院'                           => '园艺学院',
        '植物保护学院'                       => '植保学院',
        '资源与环境学院'                     => '资环学院',
    ],
];
