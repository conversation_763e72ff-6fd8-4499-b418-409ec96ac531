<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

// 应用公共文件

use think\Request;
use think\Cache;
use app\wfw\model\NoticeView;
use app\wfw\model\NoticeCreate;
use think\Response;


function RequestRateLimit($minute, $limit)
{
    // 获取用户的ip
    $real_ip = GetReverseProxyIP();
    $ip_str = md5($real_ip);
    // 获取用户访问的接口路径
    $api_path = request()->url();
    $second = $minute * 60;//分钟转换成时间戳
    // 将ip和访问的接口路径md5加密成一个字符串 ,这样就代表同一个用户访问的接口
    $UC = md5($ip_str . $api_path);
    $request_time = time();
    $request_rate_limit_table = new \app\apply\model\RequestRateLimit;
    if ($request_rate_limit_table->where('request_api', $UC)->find() == false) {
        $request_rate_limit_table
            ->data([
                'real_ip' => $real_ip,
                'time_stamp' => $request_time,
                'request_time' => date('Y-m-d H:i:s', $request_time),
                'request_api' => $UC,
                'request_num' => 1,
                'request_api_origin' => $api_path,
            ]);
        $request_rate_limit_table->save();
    } else {
        $time_stamp = $request_rate_limit_table->field('time_stamp')->where('request_api', $UC)->find();
        $time_sub = $request_time - intval($time_stamp['time_stamp']);
        //10分钟对应时间戳600000
        if ($time_sub < $second) {
            $request_num_data = $request_rate_limit_table->field('request_num')->where('request_api', $UC)->find();
            if ($request_num_data['request_num'] < $limit) {
                $request_rate_limit_table
                    ->where('request_api', $UC)
                    ->update(['request_num' => $request_num_data['request_num'] + 1]);
            } else {
                $data['code'] = 1;
                $origin_time = $request_rate_limit_table->where('request_api', $UC)->value('time_stamp');
                $time = (int)($minute - (time() - $origin_time) / 60);
                if ($time == 0) {
                    $time = 60 * ($minute - (time() - $origin_time) / 60);
                    $data['msg'] = '请在' . $time . '秒后重试！';
                } else {
                    $data['msg'] = '请在' . ($time + 1) . '分钟后重试！';
                }
                return $data;
            }
        } else {
            $request_rate_limit_table->where('request_api', $UC)->delete();
        }
    }
}
// 检查是否为微信浏览器
function IsWxBrowser() {
    $agent = Request::instance()->header("user-agent");
    return preg_match("/MicroMessenger/i", $agent) != 0;
}

// 获取反向代理原始IP地址
function GetReverseProxyIP() {
    $unknown = 'unknown';
    if ( isset($_SERVER['HTTP_X_FORWARDED_FOR']) && $_SERVER['HTTP_X_FORWARDED_FOR'] && strcasecmp($_SERVER['HTTP_X_FORWARDED_FOR'], $unknown) ) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif ( isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], $unknown) ) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    // 处理多层代理的情况
    if (false !== strpos($ip, ',')){
        $ip = reset(explode(',', $ip));
    }
    // 校验IP地址是否合法性
    if (filter_var($ip, FILTER_VALIDATE_IP)) {
        return $ip;
    } else {
        return 'null';
    }
}

// 检查UA是否合法（字符黑名单机制）
function isValidUA($ua) {
    if(!preg_match("/.*[~!@#$%^&*+|\\\<>\'\\[\\]{}\"]+/", $ua)) {
        return true;
    } else {
        return false;
    }
}

function CheckCode($code) {
    // 检查长度是否为32
    if (strlen($code) != 32) {
        return false;
    }

    // 检查是否只包含英文字母
    if (!preg_match("/^[A-Za-z]+$/", $code)) {
        return false;
    }

    // 如果都满足，返回true
    return true;
}

// 中文姓名判断
function IsChineseName($name) {
    if(preg_match('/^([\xe4-\xe9][\x80-\xbf]{2}){2,}$/', $name) && mb_strlen($name,'utf-8') <= config('SYSTEM_NAME_MAX_LENGTH')) {
        return true;
    }
    else {
        return false;
    }
}

// 手机号判断
function IsPhoneNum($phone) {
    // $chars = "/^1\d{10}$/";

    $chars = "/^1[3456789]{1}\d{9}$/";
    if(preg_match($chars, $phone) && is_numeric($phone)) {
        return true;
    }
}

// 学号判断
function IsStuNumber($stu_number) {
    if((strlen($stu_number) == 10 || strlen($stu_number) == 8 || strlen($stu_number) == 9|| strlen($stu_number) == 6 ) && is_numeric($stu_number)) {
        return true;
    }
}

// 工号判断
function IsTeaNumber($tea_number) {
    if(strlen($tea_number) == 6 && is_numeric($tea_number)) {
        return true;
    }
}

// 学号+工号判断
function IsTeaStuNumber($tea_stu_number) {
    if(IsStuNumber($tea_stu_number) || IsTeaNumber($tea_stu_number) ) {
        return true;
    }
}

// 去除空格
function removeSpace($str) {
    $str = preg_replace('/\s+/', '', $str);
    return $str;
}

// 专业判断
function IsMajor($major) {
    $major = removeSpace($major);
    if(mb_strlen($major,'utf-8') <= 0 || mb_strlen($major,'utf-8') > 30) {
        return false;
    } else {
        return true;
    }
}

// 判断性别是否合法
function IsSex($sex) {
    if ($sex != '男' && $sex != '女' ) {
        return false;
    } else  {
        return true;
    }
}

// 判断是否为公网IP地址
function isPublicIP($ip) {
    if(filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
        return true;
    } else {
        return false;
    }
}

// 判断是否含有表情包
function IsHaveEmoji($str) {
    $len = mb_strlen($str);
    $array = [];
    for ($i = 0; $i < $len; $i++) {
        $array[] = mb_substr($str, $i, 1, 'utf-8');
        if (strlen($array[$i]) >= 4) {
            return true;
        }
    }
    return false;
}

// 文本判断
function IsTxt($str) {
    $str2 = RemoveEmoji($str);
    $str2 = removeSpace($str2);
    if(mb_strlen($str2,'utf-8') == '') {
        return 1;
    } else if(mb_strlen($str2,'utf-8') > 600) {
        return 2;
    }
}

// QQ号判断
function CheckUserContact($qq_number) {
    $chars = "/^[1-9][0-9]{4,10}$/";
    if(preg_match($chars, $qq_number) && is_numeric($qq_number)) {
        return true;
    } else {
        return false;
    }
}

// 移除表情包
function RemoveEmoji($str) {
    $mbLen = mb_strlen($str);
    $strArr = [];
    for ($i = 0; $i < $mbLen; $i++) {
        $mbSubstr =  mb_substr($str, $i, 1, 'utf-8');
        if(strlen($mbSubstr) >= 4)
            continue;
        $strArr[] = $mbSubstr;
    }
    return implode('',$strArr);
}

function GetView($notice_id){
    
    //记录访问者ip、ua、time
    $visiter = new NoticeView;
    $user=[
        'notice_view_time'         => date("Y-m-d H:i:s", time()),
        'notice_view_user_ip'      => GetReverseProxyIP(),
        'notice_view_user_ua'      => Request::instance()->header('user-agent'),
        'notice_id'                => $notice_id,
        
    ];
    $visiter->save($user);
    $create_user = new NoticeCreate;
    if($notice_id !='' && $notice_id != null){
        $create_user->where('notice_id',$notice_id)->setInc('notice_views');
    }
    //返回阅读量
    $notice_views=$create_user->where('notice_id',$notice_id)->value('notice_views');
    return $notice_views;
}

// 判断设备是安卓还是iOS
function GetDeviceType(){
    // 全部变成小写字母
    $agent = strtolower(Request::instance()->header('user-agent'));
    $type = false;
    // 分别进行判断
    if(strpos($agent, 'iphone') || strpos($agent, 'ipad')){
        $type = true;
    }
    if(strpos($agent, 'android')){
        $type = false;
    }
    return $type;
}


function CheckName($name)
{
    if (preg_match('/^[\x7f-\xff]+$/', $name) && mb_strlen($name, 'utf-8') <= config('SYSTEM_NAME_MAX_LENGTH')) {
        return true;
    }
    return false;
}

// 发送GET请求
function httpGet($url)
{
    $curl = curl_init();
    
    // 指定url
    curl_setopt($curl, CURLOPT_URL, $url);
    // 读取的信息以文件流的形式返回
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    // 不返回HTTP头部信息
    curl_setopt($curl, CURLOPT_HEADER, false);

    // 关闭ssl验证
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

    // 强制使用ipv4解析
    curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);

    $str = curl_exec($curl);
    curl_close($curl);
    return $str;

}

function HttpPost($url, $json_str){
    $curl = curl_init();
    
    // 指定url
    curl_setopt($curl, CURLOPT_URL, $url);
    // 读取的信息以文件流的形式返回
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    // 不返回HTTP头部信息
    curl_setopt($curl, CURLOPT_HEADER, false);

    // 标识这个请求是POST请求
    curl_setopt($curl, CURLOPT_POST, true);
    // 提交json数据
    curl_setopt($curl, CURLOPT_POSTFIELDS, $json_str);

    // 关闭ssl验证
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

    $str = curl_exec($curl);
    curl_close($curl);
    return $str;
}

function PostResSuccess($data = "") {
    Response::create(
        (Object)array(
            "status" => "success",
            "data" => $data
        ),
        $type = "json"
    )->send();
    exit; // 直接调用内置函数 输出http结果然后结束
}

function PostResError($data = "") {
    Response::create(
        (Object)array(
            "status" => "error",
            "data" => $data
        ),
        $type = "json"
    )->send();
    exit;
}

function Error($msg = "") {
    ErrorLog($msg);
    Response::create(
        (Object)array(
            "status" => "error",
            "msg" => $msg
        ),
        $type = "json"
    )->send();
    exit;
}

function Success($msg = "") {
    Response::create(
        (Object)array(
            "status" => "success",
            "msg" => $msg
        ),
        $type = "json"
    )->send();
    exit;
}

function SetPhpFile($filename, $content) {
    file_put_contents($filename, json_encode($content));
}

// 检验文本长度是否符合要求
function checkTextLength($text, $minLength, $maxLength)
{
    $textLength = mb_strlen($text, 'UTF-8');
    if($textLength == 0)
        return "不能为空";

    if ($textLength < $minLength )
        return "至少填写 $minLength 字";
    
    if ($textLength > $maxLength)
        return "填写需小于 $minLength 字";

}

// 检验 jwt 令牌格式是否正确
function CheckJwtToken($token)
{
    // JWT 令牌的正则表达式模式
    $pattern = '/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$/';

    return preg_match($pattern, $token) === 1;
}

// 用户登录记录到日志（login_in.log）
function ErrorLog($error_msg = "") {
    $log_type = "error";
    try {
        // if (session("login_user")) {
        //     $login_time     = date('Y-m-d H:i:s',time());
        //     $user_name      = session('user_name');
        //     $user_number    = session('login_user');
        //     $user_ip        = GetRealIp();
        //     $user_ua        = Request::instance()->header('user-agent');
        //     $user_info      = "用户：$user_name($user_number)在($login_time)登录，登录方式为：($login_mode)，用户IP为: ($user_ip)，用户UA为: ($user_ua)";
        // }
        Log::record($error_msg, $log_type);
    } catch (\Throwable $e) { }
}

// 学院缩写
function SectionChange($section) {
    foreach(config('SECTION_ABBREVIATE') as $key=>$val){
        if($section == $key){
            $section = $val;
            break;
        }
    }
    return $section;
}

function CheckUserNum($user_number) {
    if (strlen($user_number) >= 2 && substr($user_number, 0, 2) === '22') {
        return true;
    } else {
        return false;
    }
}




