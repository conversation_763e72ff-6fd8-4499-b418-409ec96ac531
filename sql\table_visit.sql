-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-26 15:29:01
-- 服务器版本： 5.7.34-log
-- PHP 版本： 7.3.31

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_visit`
--

CREATE TABLE `table_visit` (
  `visit_id` bigint(50) NOT NULL,
  `visit_ip` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visit_time` datetime DEFAULT NULL,
  `visit_ua` text COLLATE utf8_unicode_ci,
  `visiter_user_name` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_number` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_section` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_henau_openid` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- 转储表的索引
--

--
-- 表的索引 `table_visit`
--
ALTER TABLE `table_visit`
  ADD PRIMARY KEY (`visit_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_visit`
--
ALTER TABLE `table_visit`
  MODIFY `visit_id` bigint(50) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
