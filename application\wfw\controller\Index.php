<?php

namespace app\wfw\controller;

use think\Request;
use app\wfw\model\Visit;
use app\wfw\model\NoticeView;
use app\wfw\model\NoticeCreate;
use app\wfw\model\IdeasUser;
use app\wfw\model\ServicesVisit;
use app\wfw\model\WfwServices;
use app\wfw\model\WfwConfig; 
use app\wfw\model\MsgLog;
use app\wfw\model\MessageVisit;
use app\wfw\model\GrayscaleServices;
use app\wfw\model\AiServiceSearchLog;
use app\wfw\model\WfwWidgetDataLog;
use org\wechat\jssdk;

class Index extends Base
{
    // 转化用户身份
    public function mapUserStatusToServiceVisitUser($userStatus) {
        $type = 0;
        if ($userStatus === 0) {
            $type = 1; // 本科生
        } else if ($userStatus === 1) {
            $type = 2; // 研究生
        } else if ($userStatus === 2) {
            $type = 3; // 在校教职工
        } else if ($userStatus === 3) {
            $type = 4; // 校后勤/外聘人员
        } else if ($userStatus === 4) {
            $type = 5; // 校友
        } else if ($userStatus === 5) {
            $type = 6; // 现代农业联合研究生院研究生
        } else {
            $type = 0; // 未知
        }
        return $type;
    }
    // 灰度服务可见性规则，接收用户身份、年级、学院/部门、尾号，返回是否允许访问
    public function canAccessGreyService($status, $grade, $college, $tail_number, $visibility_rule) {
        // 检查用户状态规则
        if (array_key_exists($status, $visibility_rule)) {
            $rules = $visibility_rule[$status];
            // 对于不同身份状态的用户分别判断
            if($status === 1) {
                // 如果本科生规则为true，直接允许访问
                if($rules == true) {
                    return true;
                }
                // 否则检查年级规则
                // isset() 函数用于检查变量是否已设置并且不为 null。
                // 检查 $rules 数组中是否存在键为 $grade 的元素，并且该元素是否为数组。
                if (isset($rules[$grade]) && is_array($rules[$grade])) {
                    // 将 $rules 数组中键为 $grade 的元素赋值给 $rule 变量。
                    $rule = $rules[$grade];
                    // 检查 $rule 是否为布尔值 true。如果规则是true，直接允许访问
                    if ($rule === true) {
                        return true;
                    }
                    // 检查 $rule 数组中是否存在键为 $college （学院）的元素。 
                    if (isset($rule[$college])) {
                        // 将 $rule 数组中键为 $college 的元素赋值给 $college_rule 变量。
                        $college_rule = $rule[$college];
                        // 如果学院规则是布尔值，直接返回结果
                        if ($college_rule === true) {
                            return true; // 允许访问
                        }
                        // 如果学院规则是数组，进一步检查尾号
                        elseif (is_array($college_rule)) {
                            // 检查尾号是否存在并且允许访问
                            return array_key_exists($tail_number, $college_rule) && $college_rule[$tail_number] === true;
                        }
                    }
                }
            }else if($status === 2) {
                // 对于研究生
                return true;
            }else if($status === 3) {
                // 对于教职工,检查部门
                return isset($rules[$college]) && $rules[$college] === true;
            }else if($status === 4) {
                return false;}
            else if($status === 5) {
                return true;}
            else if($status === 6) {
                return true;}
            else {
                return false;
            }
        }
        return false;
    }

    // 获取用户身份信息
    public function WfwGetUserInfo()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // 从 GET 请求中获取 code 参数
        $code = input('get.code');

        // 对于未传递 code 参数的请求进行拦截
        if (!$code) {
            Error('非法请求！');
        }

        // 通过传入的 code 参数，调用 httpGet 函数获取 access_token
        $data = json_decode(httpGet(config('GET_ACCESS_TOKEN_API') . '?appid=' . config('AppID') . '&secret=' . config('AppSecret') . '&code=' . $code . '&grant_type=authorization_code'));

        // 判断是否成功获取 access_token
        if (!isset($data->status) || $data->status != 'success') {
            Error($data->data);
        }

        // 从对象中获取 access_token 和 henau_openid
        $data = $data->data;
        $access_token = $data->access_token;
        $henau_openid = $data->henau_openid;

        // 通过 access_token 和 henau_openid 换取用户身份信息
        $user_info = json_decode(httpGet(config('GET_USER_INFO_API') . '?access_token=' . $access_token . '&henau_openid=' . $henau_openid));

        // 如果请求返回为空或请求状态不为 'success' 则抛出异常
        if (!isset($user_info->status) || $user_info->status != 'success') {
            Error($data->data);
        }

        // 从获取的用户身份信息中获取用户的学号、姓名、学院
        $user_data = $user_info->data;
        $user_number = $user_data->user_number;
        // $user_number = "2421211283";
        // $user_number = "2421211283";
        // $user_name = "孟桐安";
        // $user_name = "孟桐安";
        $user_name = $user_data->user_name;
        $user_section = $user_data->user_section;
        // $user_status = 2;
        $user_status = $user_data->user_status;

        // 保存访问信息到用户访问表
        $userVisit = new Visit([
            'visiter_user_name' => $user_name,
            'visiter_user_number' => $user_number,
            'visiter_user_section' => $user_section,
            'visiter_user_henau_openid' => $henau_openid,
            'visit_ip' => $trueIp,
            'visit_time' => date("Y-m-d H:i:s", time()),
            'visit_ua' => $ua,
            'visit_type' => 'wfw',
            'visiter_user_status' => $user_status,
        ]);
        $vis_count_num = $userVisit->count();
        $response = $userVisit->save();
        if (!$response)
            Error('请求失败，请稍后重试！');

        $res['user_name'] = $user_name;
        $res['user_number'] = $user_number;
        $res['user_section'] = $user_section;
        $res['user_henau_openid'] = $henau_openid;

        // 创建 JWT Token
        $token = $this->CreatJwtToken($res);

        // 判断是否局域网
        $is_henau_lan = !isPublicIP($trueIp);

        // 微服务访问量
        $vis_count = $vis_count_num > 10000 ? ($vis_count_num - $vis_count_num % 100) / 10000 . "万" : $vis_count_num;

        // 未读消息数量
        $unread_msg_count = MsgLog::where('send_user_number', $user_number)->where('is_read', null)->count();

        // 获取用户身份对应的服务访问用户类型
        $serviceVisitUser = $this->mapUserStatusToServiceVisitUser($user_status);

        // 根据用户身份获取年级信息及学号尾号，目前针对本科生进行获取，研究生及教职工、毕业生不获取
        if ($serviceVisitUser == 1) {
            $user_grade = substr($user_number, 0, 2);
            $tail_number = substr($user_number, 9, 1);
        } else {
            $user_grade = null;
            $tail_number = null;
        }

        // 获取灰度发布服务
        $grayscale_services = GrayscaleServices::where('is_active', 1)->select();

        $accessible_grey_services = []; // 存储可访问的灰度服务
        $visibility_rule = []; // 存储可见性规则
        // 检查是否有灰度服务
        if (!empty($grayscale_services)) {
            foreach ($grayscale_services as $service) {
                // 检查白名单
                $user_ids = json_decode($service->test_user_ids, true);
                if (is_array($user_ids) && in_array($user_number, $user_ids)) {
                    // 用户在白名单中，添加服务
                    $accessible_grey_services[] = [
                        'service_id' => $service->service_id,
                        'service_module_id' => $service->service_module_id,
                        'service_module_name' => $service->service_module_name,
                        'service_type' => $service->service_type,
                        'service_name' => $service->service_name,
                        'service_url' => $service->service_url,
                        'service_icon' => $service->service_icon,
                        'is_henau_lan_service' => $service->is_henau_lan_service,
                        'is_wfw_service_show' => $service->is_wfw_service_show,
                        'is_wfw_service' => $service->is_wfw_service,
                        'is_new_service' => $service->is_new_service,
                        'service_visit_user' => $service->service_visit_user, // 保持与微服务前端接收的数据格式一致
                        'hot_service' => $service->hot_service,
                        'beta_service' => 1,
                    ];
                } else {
                    // 用户不在白名单，检查可见性规则
                    $visibility_rule = json_decode($service->visibility_rule, true);
                    if ($this->canAccessGreyService($serviceVisitUser, $user_grade,         $user_section, $tail_number, $visibility_rule)) {
                        $accessible_grey_services[] = [
                            'service_id' => $service->service_id,
                            'service_module_id' => $service->service_module_id,
                            'service_module_name' => $service->service_module_name,
                            'service_type' => $service->service_type,
                            'service_name' => $service->service_name,
                            'service_url' => $service->service_url,
                            'service_icon' => $service->service_icon,
                            'is_henau_lan_service' => $service->is_henau_lan_service,
                            'is_wfw_service_show' => $service->is_wfw_service_show,
                            'is_wfw_service' => $service->is_wfw_service,
                            'is_new_service' => $service->is_new_service,
                            'service_visit_user' => $service->service_visit_user, // 保持与微服务前端接收的数据格式一致
                            'hot_service' => $service->hot_service,
                            'beta_service' => 1,
                        ];
                    }
                }
            }
        }

        // 返回服务
        $servicesList = WfwServices::field('service_id,service_module_id,service_module_name,service_type,service_name,service_description,service_function_description,service_url,service_icon,is_henau_lan_service,hot_service,service_visit_user,service_wxapp_username,is_wfw_service,is_new_service,service_order,hot_service_order')->where('is_delete',0)->where('is_wfw_service_show',1) -> where('service_visit_user', 'like', "%{$serviceVisitUser}%") -> select();
        // 对返回的服务模块进行排序
        $customOrder = [1, 2, 3, 4, 6, 7, 8, 9];
        usort($servicesList, function($a, $b) use ($customOrder) {
            // 第一优先级：模块排序
            $aIndex = array_search($a['service_module_id'], $customOrder);
            $bIndex = array_search($b['service_module_id'], $customOrder);
        
            // 处理未定义的模块（放到最后）
            $aIndex = ($aIndex !== false) ? $aIndex : PHP_INT_MAX;
            $bIndex = ($bIndex !== false) ? $bIndex : PHP_INT_MAX;
        
            if ($aIndex !== $bIndex) {
                return ($aIndex < $bIndex) ? -1 : 1;
            }
        
            // 第二优先级：service_order
            if ($a['service_order'] != $b['service_order']) {
                return ($a['service_order'] > $b['service_order']) ? -1 : 1;
            }
        
            // 第三优先级：service_id
            return ($a['service_id'] < $b['service_id']) ? -1 : 1;
        });
        // 返回灵感小站中的身份：1代表建议回复人，0代表普通用户
        $ideas_user = IdeasUser::get(['user_number' => $user_number]);
        $user_ideas_role = 0;
        if ($ideas_user) {
            $user_ideas_role = 1;
        }

        // 获取后端接口根链接
        $base_url = WfwConfig::where('module_id', 1)->where('item_id', 1)->where('is_delete', 0)->select();

        // 微信开放标签
        // $config = config('wechat');
        // $jssdkObj = new Jssdk($config['appId'], $config['appsecret'],$config['getWebUrl']);
        // $res = $jssdkObj->getSignPackage();
        // $appId      = $res['appId'];
        // $timestamp  = $res['timestamp'];
        // $nonceStr   = $res['nonceStr'];
        // $signature  = $res['signature'];

        // 返回成功响应
        $res_data = [
            'token' => $token,
            'user_name' => $user_name,
            'user_number' => $user_number,
            'user_section' => $user_section,
            'user_status' => $user_status,
            'is_henau_lan' => $is_henau_lan,
            'wfw_visit_count' => $vis_count,
            'unread_message_count' => $unread_msg_count,
            'user_ideas_role' => $user_ideas_role,
            'services_list' => $servicesList,
            'base_url' => $base_url,
            // 'wx_config' => [
            //     'appId'      =>  $appId,
            //     'timestamp'  =>  $timestamp,
            //     'nonceStr'   =>  $nonceStr,
            //     'signature'  =>  $signature,
            // ],
            'grayscale_services' => $accessible_grey_services,
        ];
        PostResSuccess($res_data);
    }

    // 获取公告点击量接口
    public function WfwGetConfigInfo()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 返回配置
        $configs = WfwConfig::field('module_id,module_name,item_id,item_name,item_url,item_type_id,item_type_name,item_content,item_time')->where('module_id','gt',1)->where('is_delete',0)->select();

        PostResSuccess($configs);
    }

    // 获取公告点击量接口
    public function WfwGetNoticeInfo()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 接收公告id并检验
        $notice_id = input('get.notice_id');
        if(!CheckId(($notice_id))){
            Error('参数错误！');
        }

        // 判断数据库是否存在对应数据
        $notice_info = NoticeCreate::get(['notice_id' => $notice_id]);
        if (empty($notice_info)) {
            Error('参数错误！');
        }

        // 获取对应公告访问量
        $viewCounts = $notice_info->notice_views;
        
        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            PostResSuccess($viewCounts);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 保存访问数据
        $userVisit = new NoticeView([
            'notice_id'                     => $notice_id,
            'notice_view_user_ip'           => $trueIp, // 获取访问者的IP
            'notice_view_user_ua'           => $ua, // 获取访问者的User-Agent
            'notice_view_time'              => date('Y-m-d H:i:s'),
            'notice_view_user_name'         => $user_info->user_name,
            'notice_view_user_number'       => $user_info->user_number,
            'notice_view_user_section'      => $user_info->user_section,
            'notice_view_user_henau_openid' => $user_info->user_henau_openid
        ]);
        $response = $userVisit->save();
        if (!$response)
            Error('请求失败，请稍后重试！');

        // 更新访问次数
        NoticeCreate::where('notice_id', $notice_id)->setInc('notice_views');

        PostResSuccess($viewCounts);
    }

    // 点击服务统计访问信息接口
    public function WfwCollectServiceVisitCount()
    {
        // 判断是否是 POST 请求
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 在请求、数据库中取出服务对应信息并判断
        $service_id = input('post.service_id');
        // 获取是否是AI推荐服务
        $is_ai_recommendation_service = input('post.isAIRecommendationService');
        
        if(!CheckId($service_id)){
            Error('参数错误！');
        }

        // 判断数据库是否存在对应数据
        $service_info = WfwServices::get(['service_id' => $service_id]);
        if (empty($service_info)) {
            Error('参数错误！');
        }

        // 保存访问数据
        $userVisit = new ServicesVisit([
            'visit_time' => date('Y-m-d H:i:s'),
            'visit_ua' => $ua,
            'visit_ip' => $trueIp,
            'visit_service_id' => $service_id,
            'visit_service_name' => $service_info->service_name,
            'visit_type' => 'wfw',
            'is_ai_recommendation_service' => $is_ai_recommendation_service,
            'visitor_name' => $user_info->user_name,
            'visitor_number' => $user_info->user_number,
            'visitor_section' => $user_info->user_section,
            'visitor_henau_openid' => $user_info->user_henau_openid
        ]);
        $response = $userVisit->save();
        if (!$response)
            ErrorLog($user_info->user_section.$user_info->user_name.'(学号：'.$user_info->user_number.')'.'在'.date('Y-m-d H:i:s', time()).'访问'.$service_info->service_name.'失败！');

        // 添加服务访问量
        WfwServices::where('service_id', $service_id)->setInc('service_visit_count');

        Success("请求成功！");
    }

    // 点击灰度测试服务统计访问信息接口
    public function WfwCollectGreyServiceVisitCount()
    {
        // 判断是否是 POST 请求
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 在请求、数据库中取出服务对应信息并判断
        $service_id = input('post.service_id');
        // 获取是否是AI推荐服务
        $is_ai_recommendation_service = input('post.isAIRecommendationService');
        if(!CheckId($service_id)){
            Error('参数错误！');
        }
        
        // 判断数据库是否存在对应数据
        $service_info = GrayscaleServices::get(['service_id' => $service_id]);
        if (empty($service_info)) {
            Error('参数错误！');
        }

        // 保存访问数据
        $userVisit = new ServicesVisit([
            'visit_time' => date('Y-m-d H:i:s'),
            'visit_ua' => $ua,
            'visit_ip' => $trueIp,
            'visit_service_id' => $service_id,
            'visit_service_name' => $service_info->service_name,
            'visit_type' => 'wfw',
            'is_ai_recommendation_service' => $is_ai_recommendation_service,
            'visitor_name' => $user_info->user_name,
            'visitor_number' => $user_info->user_number,
            'visitor_section' => $user_info->user_section,
            'visitor_henau_openid' => $user_info->user_henau_openid
        ]);
        $response = $userVisit->save();
        if (!$response)
            ErrorLog($user_info->user_section.$user_info->user_name.'(学号：'.$user_info->user_number.')'.'在'.date('Y-m-d H:i:s', time()).'访问'.$service_info->service_name.'失败！');

        // 添加服务访问量
        GrayscaleServices::where('service_id', $service_id)->setInc('service_visit_count');

        Success("请求成功！");
    }

    // 我的消息接口
    public function WfwMyMessage()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 保存访问信息到用户访问表
        $userVisit = new MessageVisit([
            'visiter_user_name' => $user_info->user_name,
            'visiter_user_number' => $user_info->user_number,
            'visiter_user_section' => $user_info->user_section,
            'visiter_user_henau_openid' => $user_info->user_henau_openid,
            'visit_ip' => $trueIp,
            'visit_time' => date("Y-m-d H:i:s", time()),
            'visit_ua' => $ua,
        ]);
        $response = $userVisit->save();
        if (!$response)
            Error('请求失败，请稍后重试！');

        // 获取我的消息，全部消息，按照时间降序排列
        $msg_data = MsgLog::field('message_title,send_time,message_content,message_url,is_read')
            ->where('send_user_number', $user_info->user_number)
            ->where('soft_delete', 0)
            ->order('log_id', 'desc')
            ->select();

        // 请求后将未读消息设为已读
        MsgLog::where('send_user_number', $user_info->user_number)->where('is_read', null)->setField('is_read', 1);

        PostResSuccess($msg_data);
    }
    // 智能服务推荐
    public function WfwAIService()
    {
        // 验证请求方法
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }
    
        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');
    
        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }
    
        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);
        $user_info = $check_res['data']->user_info;
        
        // 获取请求参数
        $servicesData = input('post.services/a');
        $serviceQuery = input('post.query/s', '');
        
        // 参数验证
        if (empty($servicesData) || !is_array($servicesData)) {
            Error('服务数据不能为空');
        }
        
        // 验证服务数据完整性
        foreach ($servicesData as $service) {
            if (!isset($service['service_module_name']) || empty($service['service_module_name']) ||
                !isset($service['service_id']) || empty($service['service_id'])) {
                Error('服务数据字段不完整');
            }
        }
    
        // 提取所有服务模块并去重
        $modules = array_unique(array_column($servicesData, 'service_module_name'));
        $moduleServices = [];
        foreach ($servicesData as $service) {
            $moduleName = $service['service_module_name'];
            $serviceName = isset($service['service_name']) ? $service['service_name'] : '未知服务';
            if (!isset($moduleServices[$moduleName])) {
                $moduleServices[$moduleName] = [];
            }
            $moduleServices[$moduleName][] = $serviceName;
        }
        
        // 构建模块描述
        $moduleDescriptions = [];
        $moduleIndex = 1;
        foreach ($modules as $module) {
            $servicesList = isset($moduleServices[$module]) ? implode('、', array_unique($moduleServices[$module])) : '';
            $moduleDescriptions[] = "{$moduleIndex}{$module}（包括：{$servicesList}）";
            $moduleIndex++;
        }
    
        // 第一步：服务分类
        $classificationPrompt = "你是一个服务分类专家，当前的服务模块以及各模块包含的服务是：\n"
            . implode("；\n", $moduleDescriptions)
            . "\n请将用户问题分类到上述服务模块，直接返回最相关的服务模块名称，不要包含其他内容。用户问题：$serviceQuery";
        $classifiedModel = 'qwen2.5:7b';
        try {
            $classificationStart = microtime(true);
            $classifiedModule = $this->callAIModel($classificationPrompt,$classifiedModel);
            $classificationLatency = round((microtime(true) - $classificationStart) * 1000, 2);
        } catch (\Exception $e) {
            Error('服务分类失败：'.$e->getMessage());
        }
        // 验证分类结果有效性
        if (!in_array(trim($classifiedModule), $modules)) {
            $this->saveLog($user_info, $serviceQuery, $classifiedModel, '0', $classificationLatency);
            PostResSuccess(['result' => '0']);
            return;
        }
    
        // 过滤出对应模块的服务
        $filteredServices = array_filter($servicesData, function($service) use ($classifiedModule) {
            return $service['service_module_name'] === trim($classifiedModule);
        });
    
        if (empty($filteredServices)) {
            $this->saveLog($user_info, $serviceQuery, $classifiedModel, '0');
            PostResSuccess(['result' => '0']);
            return;
        }
    
        // 第二步：相关性判断
        $promptContent = "当前服务模块：[".trim($classifiedModule)."]\n服务列表：\n";
        foreach ($filteredServices as $service) {
            $promptContent .= sprintf("服务ID：%s\n服务名称：%s\n功能描述：%s\n--\n",
                $service['service_id'],
                isset($service['service_name']) ? $service['service_name'] : '未知名称',
                isset($service['service_function_description']) ? $service['service_function_description'] : '无描述'
            );
        }
    
        $relevancePrompt = "你是一个服务推荐专家，根据用户搜索词：【{$serviceQuery}】\n"
            . "对以下服务进行相关性分析（评分>5才返回），直接返回符合条件的服务ID（多个用逗号分隔，没有返回0）：\n"
            . $promptContent;
        $recommendedModel = 'qwen2.5:32b';
        try {
            $recommendationStart = microtime(true);
            $finalResult = $this->callAIModel($relevancePrompt,$recommendedModel);
            $recommendationLatency = round((microtime(true) - $recommendationStart) * 1000, 2);
            $this->saveLog($user_info, $serviceQuery,  $recommendedModel,$finalResult,$recommendationLatency);
            PostResSuccess(['result' => $finalResult]);
        } catch (\Exception $e) {
            $this->saveLog($user_info, $serviceQuery, $recommendedModel, '0');
            PostResSuccess(['result' => '0']);
        }
    }
    // 获取学生考试安排
    public function WfwGetWidgetStudExamSchedule()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');

        $check_res = $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);
        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;
        $user_name = $user_info->user_name;
        $user_number = $user_info->user_number;
        $user_section = $user_info->user_section;

        try {
            // 调用考试安排API
            $apiUrl = 'https://oauth.henau.edu.cn/oauth2_data_server/getstudexamschedule';
            $params = [
                'appid' => config('AppID'),
                'secret' => config('AppSecret'),
                'user_name' => $user_name,
                'user_number' => $user_number
            ];

            // 发送API请求
            $response = httpGet($apiUrl . '?' . http_build_query($params));
            $result = json_decode($response, true);

            // 检查API返回状态
            if (!isset($result['status']) || $result['status'] !== 'success') {
                $errorMsg = isset($result['data']) ? $result['data'] : '未知错误';
                Error('获取考试安排失败：' . $errorMsg);
            }

            // 如果没有考试安排数据，返回空数组
            if (empty($result['data']['result'])) {
                PostResSuccess([]);
                return;
            }

            // 处理返回的考试安排数据
            $examSchedules = $result['data']['result'];
            
            // 获取当前日期
            $currentDate = date('Y-m-d');
            $currentTime = date('H:i');

            // 过滤掉已经结束的考试
            $futureExams = [];
            foreach ($examSchedules as $exam) {
                // 转换日期格式 26-DEC-19 -> 2019-12-26
                if (isset($exam['KSRQ']) && !empty($exam['KSRQ'])) {
                    $date = \DateTime::createFromFormat('d-M-y', $exam['KSRQ']);
                    if ($date) {
                        $formattedDate = $date->format('Y-m-d');
                        
                        // 判断逻辑：
                        // 1. 如果考试日期在当前日期之后，保留该考试
                        // 2. 如果考试日期等于当前日期，则进一步判断考试时间是否已过
                        if ($formattedDate > $currentDate) {
                            // 未来日期的考试，直接保留
                            $exam['KSRQ'] = $formattedDate;
                            $futureExams[] = $exam;
                        } else if ($formattedDate == $currentDate && isset($exam['KSSJ'])) {
                            // 当天的考试，需要判断时间
                            if ($exam['KSSJ'] > $currentTime) {
                                // 考试还未开始，保留
                                $exam['KSRQ'] = $formattedDate;
                                $futureExams[] = $exam;
                            }
                            // 如果考试已开始或结束，则不包含在结果中
                        }
                    }
                }
            }
            
            // 使用过滤后的考试安排
            $examSchedules = $futureExams;
            
            // 对考试安排按照考试日期和时间排序
            usort($examSchedules, function($a, $b) {
                // 先按日期排序
                $dateCompare = strcmp($a['KSRQ'], $b['KSRQ']);
                if ($dateCompare !== 0) {
                    return $dateCompare;
                }
                // 如果日期相同，则按时间排序
                return strcmp($a['KSSJ'], $b['KSSJ']);
            });

            $filteredExams = [];
            foreach ($examSchedules as $exam) {
                // 只选择前端需要显示的字段
                $filteredExams[] = [
                    'ZWH'  => $exam['ZWH'],             // 座位号
                    'KCMC' => $exam['KCMC'] . "考试",   // 课程名称
                    'KSRQ' => $exam['KSRQ'],            // 考试日期
                    'KSSJ' => $exam['KSSJ'],            // 考试开始时间
                    'JSSJ' => $exam['JSSJ'],            // 考试结束时间
                    'KSDD' => $exam['KSDD'],            // 考试地点
                ];
            }

            // 生成唯一的组件数据ID
            $widgetDataId = $this->generateWidgetDataId();

            // 构造返回数据，包含考试安排和考试数量
            $responseData = [
                'widget_data_id' => $widgetDataId,
                'exam_list' => $filteredExams,
                'exam_count' => count($filteredExams)
            ];

            // 记录小组件数据日志
            $this->saveWidgetDataLog([
                'widget_data_id' => $widgetDataId,
                'widget_name' => '学生考试安排',
                'user_number' => $user_number,
                'user_name' => $user_name,
                'user_section' => $user_section,
                'is_clickable' => !empty($filteredExams) ? 1 : 0,
                'data_content' => json_encode($responseData, JSON_UNESCAPED_UNICODE),
                'visit_ip' => $trueIp,
                'visit_ua' => $ua,
                'created_time' => date('Y-m-d H:i:s')
            ]);

            PostResSuccess($responseData);
        } catch (\Exception $e) {
            Error('系统异常，请稍后重试');
        }
    }

    // 获取教师监考安排
    public function WfwGetWidgetTeaExamSchedule()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res = $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'),  config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取工号等信息
        $user_info = $check_res['data']->user_info;
        $user_name = $user_info->user_name;
        // $user_name = "宗高峰";
        $user_number = $user_info->user_number;
        // $user_number = "110004";
        $user_section = $user_info->user_section;

        try {
            // 调用监考安排API
            $apiUrl = 'https://oauth.henau.edu.cn/oauth2_data_server/getteaexamschedule';
            $params = [
                'appid' => config('AppID'),
                'secret' => config('AppSecret'),
                'user_name' => $user_name,
                'user_number' => $user_number
            ];

            // 发送API请求
            $response = httpGet($apiUrl . '?' . http_build_query($params));
            $result = json_decode($response, true);
            // 检查API返回状态
            if (!isset($result['status']) || $result['status'] !== 'success') {
                $errorMsg = isset($result['data']) ? $result['data'] : '未知错误';
                if($errorMsg == "目标用户不存在或暂无其相关信息"){
                    PostResSuccess([]);
                    return;
                }
                Error('获取监考安排失败：' . $errorMsg);
            }

            // 如果没有监考安排数据，返回空数组
            if (empty($result['data']['result'])) {
                PostResSuccess([]);
                return;
            }

            // 处理返回的监考安排数据
            $examSchedules = $result['data']['result'];

            // 获取当前日期
            $currentDate = date('Y-m-d');
            $currentTime = date('H:i');

            // 过滤掉已经结束的监考
            $futureExams = [];
            foreach ($examSchedules as $exam) {
                // 转换日期格式 19-DEC-16 -> 2016-12-19
                if (isset($exam['KSRQ']) && !empty($exam['KSRQ'])) {
                    $date = \DateTime::createFromFormat('d-M-y', $exam  ['KSRQ']);
                    if ($date) {
                        $formattedDate = $date->format('Y-m-d');

                        // 判断逻辑：
                        // 1. 如果考试日期在当前日期之后，保留该考试
                        // 2. 如果考试日期等于当前日期，则进一步判断考试时间是否已过
                        if ($formattedDate > $currentDate) {
                            // 未来日期的考试，直接保留
                            $exam['KSRQ'] = $formattedDate;
                            $futureExams[] = $exam;
                        } else if ($formattedDate == $currentDate && isset($exam    ['KSSJ'])) {
                            // 当天的考试，需要判断时间
                            if ($exam['KSSJ'] > $currentTime) {
                                // 考试还未开始，保留
                                $exam['KSRQ'] = $formattedDate;
                                $futureExams[] = $exam;
                            }
                            // 如果考试已开始或结束，则不包含在结果中
                        }
                    }
                }
            }

            // 使用过滤后的监考安排
            $examSchedules = $futureExams;

            // 对监考安排按照考试日期和时间排序
            usort($examSchedules, function($a, $b) {
                // 先按日期排序
                $dateCompare = strcmp($a['KSRQ'], $b['KSRQ']);
                if ($dateCompare !== 0) {
                    return $dateCompare;
                }
                // 如果日期相同，则按时间排序
                return strcmp($a['KSSJ'], $b['KSSJ']);
            });

            $filteredExams = [];
            foreach ($examSchedules as $exam) {
                // 只选择前端需要显示的字段
                $filteredExams[] = [
                    'KCMC' => $exam['KCMC'] . "监考",   // 课程名称
                    'KSRQ' => $exam['KSRQ'],            // 考试日期
                    'KSSJ' => $exam['KSSJ'],            // 考试开始时间
                    'JSSJ' => $exam['JSSJ'],            // 考试结束时间
                    'KSDD' => $exam['KSDD'],            // 考试地点
                ];
            }

            // 生成唯一的组件数据ID
            $widgetDataId = $this->generateWidgetDataId();

            // 构造返回数据，包含监考安排和监考数量
            $responseData = [
                'widget_data_id' => $widgetDataId,
                'exam_list' => $filteredExams,
                'exam_count' => count($filteredExams)
            ];

            // 记录小组件数据日志
            $this->saveWidgetDataLog([
                'widget_data_id' => $widgetDataId,
                'widget_name' => '教师监考安排',
                'user_number' => $user_number,
                'user_name' => $user_name,
                'user_section' => $user_section,
                'is_clickable' => !empty($filteredExams) ? 1 : 0,
                'data_content' => json_encode($responseData, JSON_UNESCAPED_UNICODE),
                'visit_ip' => $trueIp,
                'visit_ua' => $ua,
                'created_time' => date('Y-m-d H:i:s')
            ]);

            PostResSuccess($responseData);
        } catch (\Exception $e) {
            Error('系统异常，请稍后重试');
        }
    }

    public function WfwGetWidgetVisitorReservationInfo()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res = $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'),  config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取用户信息
        $user_info = $check_res['data']->user_info;
        $user_name = $user_info->user_name;
        $user_number = $user_info->user_number;
        $user_section = $user_info->user_section;

        try {
            // 模拟API返回的访客信息数据
            // $mockVisitorData = [
            //     "status" => "success",
            //     "data" => [
            //         "result" => [
            //             [
            //                 "visitor_username" => "李四",
            //                 "interviewee_name" => $user_name,
            //                 "interviewee_idnum" => $user_number,
            //                 "appointment_start" => date('Y-m-d H:i:s',strtotime            ('+2 hours')), // 小时后开始
            //                 "appointment_end" => date('Y-m-d H:i:s',strtotime          ('+5 hours')), // 5时后结束
            //                 "status" => 3
            //             ],
            //             [
            //                 "visitor_username" => "王五",
            //                 "interviewee_name" => $user_name,
            //                 "interviewee_idnum" => $user_number,
            //                 "appointment_start" => date('Y-m-d H:i:s',strtotime            ('+1 day +9hours')), // 明天上午9点
            //                 "appointment_end" => date('Y-m-d H:i:s',strtotime          ('+1 day +12hours')), // 明天中午12点
            //                 "status" => 2
            //             ],
            //             [
            //                 "visitor_username" => "赵六",
            //                 "interviewee_name" => $user_name,
            //                 "interviewee_idnum" => $user_number,
            //                 "appointment_start" => date('Y-m-d H:i:s',strtotime            ('+2 days +14hours')), // 后天下午2点
            //                 "appointment_end" => date('Y-m-d H:i:s',strtotime          ('+2 days +17hours')), // 后天下午5点
            //                 "status" => 1
            //             ]
            //         ]
            //     ]
            // ];
            // 使用模拟数据替代API调用
            // $result = $mockVisitorData;
            // 模拟结束

            // 调用访客信息API
            $apiUrl = 'https://oauth.henau.edu.cn/oauth2_data_server/getvisitorinfo';
            $params = [
                'appid' => config('AppID'),
                'secret' => config('AppSecret'),
                'user_name' => $user_name,
                'user_number' => $user_number
            ];

            // // 发送API请求
            $response = httpGet($apiUrl . '?' . http_build_query($params));
            $result = json_decode($response, true);

            // 检查API返回状态
            if (!isset($result['status']) || $result['status'] !== 'success') {
                $errorMsg = isset($result['data']) ? $result['data'] : '未知错误';
                Error('获取访客信息失败：' . $errorMsg);
            }

            // 如果没有访客信息数据，返回空数组
            if (empty($result['data']['result'])) {
                PostResSuccess([]);
                return;
            }

            // 处理返回的访客信息数据
            $visitorInfos = $result['data']['result'];

            // 获取当前时间
            $currentDateTime = date('Y-m-d H:i:s');

            // 过滤访客信息，可以根据需要进行时间或状态过滤
            $filteredVisitors = [];
            foreach ($visitorInfos as $visitor) {
                // 检查审批状态：只保留1（待受访者审批）、2（待部门领导审批）、3（已批准）
                if (!in_array($visitor['status'], [1, 2])) {
                    continue; // 跳过已拒绝（4）和其他未知状态
                }
                
                // 检查预约结束时间是否在当前时间之后
                if (isset($visitor['appointment_end']) && !empty($visitor['appointment_end'])) {
                    // 将预约结束时间与当前时间进行比较
                    if (strtotime($visitor['appointment_end']) <= strtotime($currentDateTime)) {
                        continue; // 跳过已经超过预约结束时间的访客信息
                    }
                }
                
                // 添加状态描述
                $statusText = '';
                switch ($visitor['status']) {
                    case 1:
                        $statusText = '待我审批';
                        break;
                    case 2:
                        $statusText = '待部门领导审批';
                        break;
                    case 3:
                        $statusText = '已批准';
                        break;
                    default:
                        $statusText = '未知状态';
                }
                 // 构建访客数据
                $visitorData = [
                    'visitor_username' => $visitor['visitor_username'],        // 访客姓名
                    'appointment_start' => $visitor['appointment_start'],      // 到访时间
                    'appointment_end' => $visitor['appointment_end'],          // 离开时间
                    'status' => $visitor['status'],                            // 审批状态
                    'status_text' =>$statusText                               // 状态描述
                ];
            
                // 对于状态为1和2的数据，添加widget_url字段
                if (in_array($visitor['status'], [1])) {
                    $visitorData['widget_url'] = 'https://bwcfr.henau.edu.cn/visitorVerify/#/pages/index/index';
                }
            
                $filteredVisitors[] = $visitorData;
            }

            // 对访客信息按照到访时间排序（最新的在前）
            usort($filteredVisitors, function($a, $b) {
                return strcmp($b['appointment_start'], $a   ['appointment_start']);
            });

            // 生成唯一的组件数据ID
            $widgetDataId = $this->generateWidgetDataId();

            // 构造返回数据，包含访客信息和访客数量
            $responseData = [
                'widget_data_id' => $widgetDataId,
                'visitor_list' => $filteredVisitors,
                'visitor_count' => count($filteredVisitors),
            ];

            // 记录小组件数据日志
            $this->saveWidgetDataLog([
                'widget_data_id' => $widgetDataId,
                'widget_name' => '访客预约信息',
                'user_number' => $user_number,
                'user_name' => $user_name,
                'user_section' => $user_section,
                'is_clickable' => !empty($filteredVisitors) ? 1 : 0,
                'data_content' => json_encode($responseData, JSON_UNESCAPED_UNICODE),
                'visit_ip' => $trueIp,
                'visit_ua' => $ua,
                'created_time' => date('Y-m-d H:i:s')
            ]);

            PostResSuccess($responseData);
        } catch (\Exception $e) {
            Error('系统异常，请稍后重试');
        }
    }

    /**
     * 获取我的失物招领数量
     */
    public function WfwGetWidgetMySwzlCount()
    {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res = $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取用户信息
        $user_info = $check_res['data']->user_info;
        $user_name = $user_info->user_name;
        $user_number = $user_info->user_number;
        $user_section = $user_info->user_section;

        try {
            // 调用失物招领API
            $apiUrl = 'https://swzl.henau.edu.cn/swzl/open_auth/getmyswzlcount';
            $params = [
                'appid' => config('AppID'),
                'secret' => config('AppSecret'),
                'user_name' => $user_name,
                'user_number' => $user_number
            ];

            // 发送API请求
            $response = httpGet($apiUrl . '?' . http_build_query($params));
            $result = json_decode($response, true);

            // 检查API返回状态
            if (!isset($result['status']) || $result['status'] !== 'success') {
                $errorMsg = isset($result['data']) ? $result['data'] : '未知错误';
                Error('获取失物招领信息失败：' . $errorMsg);
            }

            // 处理返回的数据
            $swzlData = $result['data'];
            
            // 生成唯一的组件数据ID
            $widgetDataId = $this->generateWidgetDataId();
            
            // 构造返回数据
            $responseData = [
                'widget_data_id' => $widgetDataId,
                'swzl_count' => isset($swzlData['count']) ? $swzlData['count'] : 0,
                'swzl_list' => isset($swzlData['list']) ? $swzlData['list'] : [],
                'is_clickable' => !empty($swzlData['list']) ? 1 : 0
            ];

            // 记录小组件数据日志
            $this->saveWidgetDataLog([
                'widget_data_id' => $widgetDataId,
                'widget_name' => '我的失物招领',
                'user_number' => $user_number,
                'user_name' => $user_name,
                'user_section' => $user_section,
                'is_clickable' => $responseData['is_clickable'],
                'data_content' => json_encode($responseData, JSON_UNESCAPED_UNICODE),
                'visit_ip' => $trueIp,
                'visit_ua' => $ua,
                'created_time' => date('Y-m-d H:i:s')
            ]);

            PostResSuccess($responseData);
        } catch (\Exception $e) {
            Error('系统异常，请稍后重试：' . $e->getMessage());
        }
    }
    /**
     * 记录用户点击小组件
     */
    public function WfwRecordWidgetClick()
    {
        // 判断是否是 POST 请求
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res = $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 获取组件数据ID
        $widgetDataId = input('post.widget_data_id/s');
        if (empty($widgetDataId)) {
            Error('组件数据ID不能为空');
        }

        try {
            // 查找对应的小组件数据记录
            $widgetLog = WidgetDataLog::where('widget_data_id', $widgetDataId)->find();
            if (!$widgetLog) {
                Error('组件数据不存在');
            }

            // 检查是否可点击
            if ($widgetLog['is_clickable'] != 1) {
                Error('该组件不可点击');
            }

            // 更新点击状态
            $updateData = [
                'is_clicked' => 1,
                'clicked_time' => date('Y-m-d H:i:s')
            ];
            
            $result = WidgetDataLog::where('widget_data_id', $widgetDataId)->update($updateData);
            
            if ($result) {
                Success('点击记录成功');
            } else {
                Error('点击记录失败');
            }
        } catch (\Exception $e) {
            Error('系统异常，请稍后重试：' . $e->getMessage());
        }
    }

    /**
     * 生成唯一的组件数据ID
     * @return string
     */
    private function generateWidgetDataId()
    {
        do {
            // 生成32位随机字符串
            $widgetDataId = md5(uniqid() . microtime() . mt_rand());
            
            // 检查是否已存在
            $exists = WidgetDataLog::where('widget_data_id', $widgetDataId)->find();
        } while ($exists);
        
        return $widgetDataId;
    }

    /**
     * 保存小组件数据日志
     * @param array $logData 日志数据
     */
    private function saveWidgetDataLog($logData)
    {
        try {
            $widgetLog = new WidgetDataLog($logData);
            $widgetLog->save();
        } catch (\Exception $e) {
            // 记录日志错误，但不影响主流程
            \think\Log::error('小组件数据日志记录失败：' . $e->getMessage());
        }
    }
    
    /**
     * 调用农大智能大模型
     * @param array $prompt 提示词
     * @param array $model 模型
     */
    private function callAIModel($prompt,$model)
    {
        $startTime = microtime(true);
        $apiUrl = 'https://oauth.henau.edu.cn/oauth2_ai_server/llm_completions';
        $params = [
            'appid'    => config('AppID'),
            'secret'   => config('AppSecret'),
        ];
        $postData = [
            'prompt' => $prompt,
            'model'  => $model,
            'stream' => false
        ];
        // PostResSuccess($postData);
        $response = httpPost($apiUrl.'?'.http_build_query($params), json_encode($postData));
        $result = json_decode($response, true);
        if (!isset($result['choices'][0]['message']['content'])) {
            throw new \Exception('AI服务响应异常');
        }
        
        return trim($result['choices'][0]['message']['content']);
    }

    /**
     * 记录用户查询日志
     * @param object $user_info 用户信息
     * @param string $query 查询内容
     * @param string $model 模型
     * @param string $response 响应内容
     * @param string $latency 延迟时间
     */
    private function saveLog($user_info, $query, $model,$response,$latency = 0)
    {
        $logData = [
            'user_number' => $user_info->user_number,
            'user_name'   => $user_info->user_name,
            'query'       => $query,
            'model'       => $model,
            'response'    => $response,
            'latency'     => $latency,
            'created_time'=> date('Y-m-d H:i:s'),
        ];
        
        try {
            (new AIServiceSearchLog())->insert($logData);
        } catch (\Exception $e) {
            \think\Log::error('日志记录失败：'.$e->getMessage());
        }
    }
}
