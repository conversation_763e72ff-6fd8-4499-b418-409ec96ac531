<?php

namespace app\getmac\controller;

use app\getmac\model\Getmac;
use app\getmac\model\Visit;
use think\Request;

class Submit extends Base
{
    // 定义函数名为 getUserInfo，用于获取用户身份信息
    public function GetUserInfo() {
        // 判断是否是get请求
        if (!Request::instance()->isGet())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        // 获取用户的User-Agent信息
        $ua = Request::instance()->header('user-agent');
        // 如果获取的用户ip地址为'null'或者User-Agent信息非法 
        if ($true_ip === 'null' || !isValidUA($ua)) {
            // 返回非法访问的错误信息
            Error('非法请求！');
        }

        // 从 GET 请求中获取 code 参数
        $code = input('get.code');

        // 对于未传code参数的请求进行拦截
        if(!$code) {
            Error('非法请求！');
        }

        // 对于格式不正确的code，禁止其访问
        // if(!CheckCode($code))
        //     Error("错误的code格式");

        // 通过传入的 code 参数，调用 httpGet 函数获取 access_token
        $data = json_decode(httpGet(config('GET_ACCESS_TOKEN_API') . '?appid=' . config('AppID') . '&secret=' . config('AppSecret') . '&code=' . $code . '&grant_type=authorization_code'));
        // 判断是否成功获取access_token
        if(!$data || $data && $data->status != "success") {
            Error($data->data);
        }

        // 从对象中获取 access_token 和 henau_openid
        $data = $data->data;
        // 获取的access_token
        $access_token = $data->access_token;
        // 获取的用户身份唯一标识符
        $henau_openid = $data->henau_openid;

        // 通过 access_token 和 henau_openid换取用户身份信息
        $user_info = json_decode(httpGet(config('GET_USER_INFO_API') . '?access_token=' .$access_token . '&henau_openid=' . $henau_openid));
        // 如果请求返回为空或请求状态不为success则返回报错
        if(!$user_info || $user_info && $user_info->status != 'success') {
            Error($data->data);
        }

        $user_number    = $user_info->data->user_number;
        $user_name      = $user_info->data->user_name;
        $user_section   = $user_info->data->user_section;


        // 访问记录写入Visit
        $visit = new Visit([
            'visit_time'            => date("Y-m-d H:i:s", time()),
            'visitor_ip_info'       => $true_ip,
            'visitor_ua_info'       => $ua,
            'visitor_henau_openid'  => $henau_openid,
            'visitor_name'          => $user_name,
            'visitor_section'       => $user_section,
            'visitor_number'        => $user_number,
        ]);
        $visit->save();

        // 从获取的用户身份信息中获取用户的学号、姓名、学院，并保存到数组 $user 中
        // 用户学 / 工号
        $user_number = $user_info->data->user_number;
        // 用户姓名
        $user_name = $user_info->data->user_name;
        // 用户所在学院或部门
        $user_section = $user_info->data->user_section;
        // 用户手机号

        $res['user_name']          = $user_name;
        $res['user_number']        = $user_number;
        $res['user_section']       = $user_section;
        $res['user_henau_openid']  = $henau_openid;

        $token = $this->CreatJwtToken($res);
        $res_data = ['token'        => $token,
                    'user_name'     => $user_name,
                    'user_number'   => $user_number,
                    'user_section'  => $user_section,
        ];

        PostResSuccess($res_data);
    }

    // 提交信息接口
    public function MacAddressSubmit() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        $mac_address = input('post.mac_address');
        $submitter_remark = input('post.submitter_remark');
        $jwt_token = input('post.jwt_token');

        if (empty($jwt_token))
            Error('无效的token');

        // 判断是否为非法格式数据
        if (is_array($mac_address) || is_array($jwt_token) || is_array($submitter_remark))
            Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error("密钥已失效，请重新访问系统");

        if (mb_strlen($mac_address, 'UTF-8') == 0)
            Error("MAC地址不能为空");
    
        if (mb_strlen($mac_address, 'UTF-8') > config("MAC_ADDRESS_MAX_LENGTH"))
            Error("MAC地址长度应小于".config("MAC_ADDRESS_MAX_LENGTH")."个字符");

        if (mb_strlen($submitter_remark, 'UTF-8') > config("REMARK_TEXT_MAX_LENGTH"))
            Error("备注信息长度应小于".config("REMARK_TEXT_MAX_LENGTH")."字");
        
        // 对请求频次进行限制
        $data = RequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1)
            Error($data['msg']);

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;

        $info = new Getmac([
            'submitter_openid'          => $user_info->user_henau_openid,
            'submitter_name'            => $user_info->user_name,
            'submitter_number'          => $user_info->user_number,
            'submitter_section'         => $user_info->user_section,
            'mac_address'               => $mac_address,
            'submitter_remark'          => $submitter_remark,
            'submit_time'               => date('Y-m-d H:i:s', time()),
            'submitter_ip_info'         => $true_ip,
            'submitter_ua_info'         => $ua,
        ]);

        $res = $info->save();
        if (!$res)
            Error('请求失败，请稍后重试！');

        Success('提交成功！');
    }
}