<?php

namespace app\wfw\controller;

use think\Request;
use think\Controller;
use app\wfw\model\Visit;
use app\wfw\model\NoticeView;
use app\wfw\model\NoticeCreate;
use app\wfw\model\IdeasUser;
use app\wfw\model\ServicesVisit;
use app\wfw\model\WfwServices;
use app\wfw\model\WfwConfig;
use app\wfw\model\MsgLog;
use app\wfw\model\MessageVisit;
use app\wfw\model\HenauNotice;


class Open extends Controller
{
    // 转化用户身份
    public function mapUserStatusToServiceVisitUser($userStatus)
    {
        $type = 0;
        if ($userStatus === 0) {
            $type = 1; // 本科生
        } else if ($userStatus === 1) {
            $type = 2; // 研究生
        } else if ($userStatus === 2) {
            $type = 3; // 在校教职工
        } else if ($userStatus === 3) {
            $type = 4; // 校后勤/外聘人员
        } else if ($userStatus === 4) {
            $type = 5; // 校友
        } else if ($userStatus === 5) {
            $type = 6; // 现代农业联合研究生院研究生
        } else {
            $type = 0; // 未知
        }
        return $type;
    }

    /**
     * 获取服务列表接口
     */
    public function OpenGetServicesList()
    {
        // 判断是否是 Post 请求
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }
        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // 判断是否在 IP 白名单中
        if (!$this->checkIpWhitelist($trueIp)) {
            return json(['code' => 403, 'msg' => 'IP未授权'], 403);
        }
        // 获取请求对象
        $request = Request::instance();

        // 验证接口密钥
        $authSecret = $request->post('auth_secret');
        if (!$authSecret || $authSecret !== config('OpenGetServicesList_KEY')) {
            return json(['code' => 401, 'msg' => '认证失败'], 401);
        }

        // 获取请求参数
        $user_status = $request->post('user_status');
        $user_status_int = intval($user_status);
        
        // tp5不能用post去接收数组[1,2,3,4,5]这种数据,解决办法之一就是用/a
        $notInIds = $request->post('notInServiceId/a');

        $serviceVisitUser = $this->mapUserStatusToServiceVisitUser($user_status_int);

        $servicesList = WfwServices::field('service_id,service_module_id,service_module_name,service_type,service_name,service_url,service_icon,is_henau_lan_service,hot_service,service_visit_user,service_wxapp_username,is_wfw_service,is_new_service')->where('is_delete',0)->where('is_wfw_service_show',1) -> where('service_visit_user', 'like', "%{$serviceVisitUser}%") -> select();

        // 过滤 notInServiceId 中的服务
        $filteredServices = [];
        foreach ($servicesList as $service) {
            if (!in_array($service['service_id'], $notInIds)) {
                $filteredServices[] = $service;
            }
        }
        PostResSuccess($filteredServices);
    }

    /**
     * 修改后的IP白名单校验（精确匹配）
     */
    private function checkIpWhitelist($ip)
    {
        return in_array($ip, config('OpenGetServicesList_IP'));
    }
}
