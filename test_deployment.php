<?php
/**
 * 微服务后端部署测试脚本
 * 用于验证phpStudy环境配置是否正确
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

$tests = [];
$allPassed = true;

// 1. PHP版本检查
$phpVersion = PHP_VERSION;
$phpVersionOk = version_compare($phpVersion, '5.4.0', '>=');
$tests['php_version'] = [
    'name' => 'PHP版本检查',
    'status' => $phpVersionOk ? 'PASS' : 'FAIL',
    'message' => "PHP版本: {$phpVersion} " . ($phpVersionOk ? '(符合要求 >= 5.4.0)' : '(版本过低，需要 >= 5.4.0)'),
    'required' => true
];
if (!$phpVersionOk) $allPassed = false;

// 2. 必需PHP扩展检查
$requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    $tests["ext_{$ext}"] = [
        'name' => "PHP扩展: {$ext}",
        'status' => $loaded ? 'PASS' : 'FAIL',
        'message' => $loaded ? '已加载' : '未加载',
        'required' => true
    ];
    if (!$loaded) $allPassed = false;
}

// 3. 目录权限检查
$writableDirs = [
    __DIR__ . '/runtime',
    __DIR__ . '/runtime/cache',
    __DIR__ . '/runtime/log'
];

foreach ($writableDirs as $dir) {
    $dirName = basename($dir);
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
    $writable = is_writable($dir);
    $tests["dir_{$dirName}"] = [
        'name' => "目录权限: {$dirName}",
        'status' => $writable ? 'PASS' : 'WARN',
        'message' => $writable ? '可写' : '不可写',
        'required' => false
    ];
}

// 4. 数据库连接测试
try {
    // 测试wfw模块数据库连接
    $wfwConfig = include __DIR__ . '/application/wfw/database.php';
    $dsn = "mysql:host={$wfwConfig['hostname']};port={$wfwConfig['hostport']};dbname={$wfwConfig['database']};charset={$wfwConfig['charset']}";
    $pdo = new PDO($dsn, $wfwConfig['username'], $wfwConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $tests['db_wfw'] = [
        'name' => '数据库连接: db_wfw_240916',
        'status' => 'PASS',
        'message' => '连接成功',
        'required' => true
    ];
    
    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'table_%'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $tableCount = count($tables);
    
    $tests['db_tables'] = [
        'name' => '数据库表检查',
        'status' => $tableCount > 0 ? 'PASS' : 'WARN',
        'message' => "找到 {$tableCount} 个数据表",
        'required' => false
    ];
    
} catch (Exception $e) {
    $tests['db_wfw'] = [
        'name' => '数据库连接: db_wfw_240916',
        'status' => 'FAIL',
        'message' => '连接失败: ' . $e->getMessage(),
        'required' => true
    ];
    $allPassed = false;
}

// 5. ThinkPHP框架检查
$thinkPath = __DIR__ . '/thinkphp/start.php';
$thinkExists = file_exists($thinkPath);
$tests['thinkphp'] = [
    'name' => 'ThinkPHP框架',
    'status' => $thinkExists ? 'PASS' : 'FAIL',
    'message' => $thinkExists ? '框架文件存在' : '框架文件缺失',
    'required' => true
];
if (!$thinkExists) $allPassed = false;

// 6. 路由配置检查
$routeFile = __DIR__ . '/application/route.php';
$routeExists = file_exists($routeFile);
$tests['routes'] = [
    'name' => '路由配置',
    'status' => $routeExists ? 'PASS' : 'FAIL',
    'message' => $routeExists ? '路由文件存在' : '路由文件缺失',
    'required' => true
];
if (!$routeExists) $allPassed = false;

// 7. Web服务器检查
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
$tests['webserver'] = [
    'name' => 'Web服务器',
    'status' => 'INFO',
    'message' => $serverSoftware,
    'required' => false
];

// 输出结果
$result = [
    'overall_status' => $allPassed ? 'PASS' : 'FAIL',
    'message' => $allPassed ? '部署检查通过，可以开始使用' : '部署检查发现问题，请修复后重试',
    'timestamp' => date('Y-m-d H:i:s'),
    'tests' => $tests,
    'next_steps' => $allPassed ? [
        '1. 访问API接口进行功能测试',
        '2. 检查日志文件确保无错误',
        '3. 配置具体的业务参数'
    ] : [
        '1. 修复标记为FAIL的问题',
        '2. 重新运行此测试脚本',
        '3. 查看phpStudy和PHP错误日志'
    ]
];

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
