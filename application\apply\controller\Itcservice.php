<?php

namespace app\apply\controller;

use app\apply\model\Apply;
use app\apply\model\User;
use app\apply\model\App;
use app\apply\model\Visit;
use think\Request;

class Itcservice extends Base
{
    // 定义函数名为 getUserInfo，用于获取用户身份信息
    public function GetUserInfo() {
        // 判断是否是get请求
        if (!Request::instance()->isGet())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        // 获取用户的User-Agent信息
        $ua = Request::instance()->header('user-agent');
        // 如果获取的用户ip地址为'null'或者User-Agent信息非法 
        if ($true_ip === 'null' || !isValidUA($ua)) {
            // 返回非法访问的错误信息
            Error('非法请求！');
        }

        // 从 GET 请求中获取 code 参数
        $code = input('get.code');

        // 对于未传code参数的请求进行拦截
        if(!$code) {
            Error('非法请求！');
        }

        // 对于格式不正确的code，禁止其访问
        // if(!CheckCode($code))
        //     Error("错误的code格式");

        // 通过传入的 code 参数，调用 httpGet 函数获取 access_token
        $data = json_decode(httpGet(config('GET_ACCESS_TOKEN_API') . '?appid=' . config('AppID') . '&secret=' . config('AppSecret') . '&code=' . $code . '&grant_type=authorization_code'));
        // 判断是否成功获取access_token
        if(!$data || $data && $data->status != "success") {
            Error($data->data);
        }

        // 从对象中获取 access_token 和 henau_openid
        $data = $data->data;
        // 获取的access_token
        $access_token = $data->access_token;
        // 获取的用户身份唯一标识符
        $henau_openid = $data->henau_openid;

        // 通过 access_token 和 henau_openid换取用户身份信息
        $user_info = json_decode(httpGet(config('GET_USER_INFO_API') . '?access_token=' .$access_token . '&henau_openid=' . $henau_openid));
        // 如果请求返回为空或请求状态不为success则返回报错
        if(!$user_info || $user_info && $user_info->status != 'success') {
            Error($data->data);
        }

        $user_number    = $user_info->data->user_number;
        $user_name      = $user_info->data->user_name;
        $user_section   = $user_info->data->user_section;
        $user_phone     = $user_info->data->user_phone;
        $user_status    = $user_info->data->user_status;

        // 对用户身份进行判断校验
        if($user_status == 4){
            Error('非在校生，无法使用本服务！');
        }

        $table_user     = User::get(['number' => $user_number]);

        // 如果管理员未绑定则自动绑定henau_openid
        if($table_user && $table_user->henau_openid == '') {
            $table_user->henau_openid  = $henau_openid;
            $table_user->save();
        }

        // 访问记录写入Visit
        $visit = new Visit([
            'visit_time'            => date("Y-m-d H:i:s", time()),
            'visit_ip'              => $true_ip,
            'visit_ua'              => $ua,
            'visit_url'             => Request::instance()->url(true),
            'visiter_henau_openid'  => $henau_openid,
            'visiter_name'          => $user_name,
            'visiter_section'       => $user_section,
        ]);
        $visit->save();

        // 从获取的用户身份信息中获取用户的学号、姓名、学院，并保存到数组 $user 中
        // 用户学 / 工号
        $user_number = $user_info->data->user_number;
        // 用户姓名
        $user_name = $user_info->data->user_name;
        // 用户所在学院或部门
        $user_section = $user_info->data->user_section;
        // 用户手机号
        $user_phone = $user_info->data->user_phone;

        // 在数据库中查找用户信息，并将用户的角色信息保存到 $user
        $user = User::get(['number' => $user_number]);
        // 初始化权限掩码和前端是否展示管理员信息
        $res['role_mask']     = 0;
        $res['user_role']     = 1;
        if($user) {
            // 判断是否有审批权限
            if($user->role_mask != 0) {
                $res['user_role'] = 0;
            }

            $res['role_mask']     = $user->role_mask;
        }
        $res['user_name']          = $user_name;
        $res['user_number']        = $user_number;
        $res['user_section']       = $user_section;
        $res['user_henau_openid']  = $henau_openid;
        $res['user_phone']         =$user_phone;

        $token = $this->CreatJwtToken($res);
        $res_data = ['token'        => $token,
                    'user_role'     => $res['user_role'],
                    'user_name'     => $user_name,
                    'user_number'   => $user_number,
                    'user_section'  => $user_section,
                    'user_phone'    => $user_phone,
        ];

        PostResSuccess($res_data);
    }

    // 提交信息接口
    public function EmailApply() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        // referer判断
        // if(Request::instance()->header('REFERER')) {
        //     return json('非法请求！',403);
        // }

        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        $phone      = input('post.apply_phone_number');
        // $email      = input('post.apply_email');
        $reason     = input('post.apply_reason');
        $jwt_token  = input('post.jwt_token');

        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number       = $user_info->user_number;

        $app_data = App::get(['app_id' => 1]);
        if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
            Error('当前用户无申请权限！');
        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        // 驳回原因是否符合要求
        if($res = checkTextLength($reason,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("申请缘由".$res);

        $user_name         = $user_info->user_name;
        $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;
        // 接收附件
        $file = request()->file('image');

        // 如果用户未上传附件
        if(!$file)
            Error('请先上传申请表');

        // 如果附件存在
        // 验证文件是否合法
        $validate = $this->validate(
            ['image' => $file],
            ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        );
        if (true !== $validate) {
            // 验证不通过，返回错误信息
            Error($validate);
        }

       // 移动附件到指定的目录
       $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

       if(!$info) 
           Error('请求失败，请稍后重试！');

        // 获取上传文件的网络访问路径
        $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());

        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();

        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => $reason,
            'apply_attachment'           => $save_name,
            'apply_app_id'               => 1, // 电子邮箱申请
            'apply_app_name'             => $app_data->app_name, // 电子邮箱申请
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);

        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }

        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToEmailApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);
        if($res->status == 'error')
            ErrorLog("向电子邮箱申请审批人发送通知失败");

        Success('提交成功！');
    }

    // 管理员审批接口
    public function ApplyApproval() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        // 获取用户的User-Agent信息
        $ua = Request::instance()->header('user-agent');
        // 如果获取的用户ip地址为'null'或者User-Agent信息非法 
        if ($true_ip === 'null' || !isValidUA($ua)) {
            // 返回非法访问的错误信息
            Error('非法请求！');
        }

        $jwt_token = input('post.jwt_token');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        //获取请求参数
        $apply_id               = input('post.apply_id');
        $remark                 = input('post.remark');
        $approval_res           = input('post.approval_res');

        if ($approval_res !== "1" && $approval_res !== "2" )
            Error('非法请求！');

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $approval_user_name      = $user_info->user_name;
        $approval_user_number    = $user_info->user_number;
        $approval_user_section   = $user_info->user_section;
        $approval_user_role_mask = $user_info->role_mask;
        $apply =  Apply::get(['apply_id' => $apply_id]);
        if((!($approval_user_role_mask & 1 << ((int)$apply->apply_app_id - 1))))
            Error("当前用户没有审批权限！");

        // 如果已经审批过则返回错误
        if($apply->apply_status != 0 )
            Error('错误请求！');

        // 检验申请原因是否符合要求
        if($approval_res == "2" ) {
            if($res = checkTextLength($remark,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH'))  ) {
                Error("驳回缘由".$res);
            }

            $message = '已被驳回';
        }
        else {
            $message = '已通过审核';

            // 增加申请已通过次数
            $app_data = App::where('app_id',$apply->apply_app_id)->setInc('app_apply_pass_num');
            if(!$app_data)
                ErrorLog("已通过申请次数增加失败");
        }

        // $apply_user_henau_openid =  Apply::where('apply_id', $apply_id)->value('apply_user_henau_openid');

        $apply->apply_status = $approval_res;
        $apply->approval_user_name = $approval_user_name;
        $apply->approval_user_number = $approval_user_number;
        $apply->approval_user_section = $approval_user_section;
        $apply->remark = $remark;
        $apply->approval_time = date('Y-m-d H:i:s' ,time());
        $res = $apply->save();
        if(!$res)
            Error("系统错误，请重新审批");
 
        switch ($apply->apply_app_id)
        {
        case 1:
            $message_res = SendToEmailApply($apply->apply_user_henau_openid,$message);
            break;  
        case 2:
            $message_res = SendToVpnApply($apply->apply_user_henau_openid,$message);
            break;  
        case 3:
            $message_res = SendToIpAddressApply($apply->apply_user_henau_openid,$message);
            break;  
        case 4:
            $message_res = SendToServerApply($apply->apply_user_henau_openid,$message);
            break;  
        case 5:
            $message_res = SendToDomainApply($apply->apply_user_henau_openid,$message);
            break;  
        case 6:
            $message_res = SendToWIFIApply($apply->apply_user_henau_openid,$message);
            break;
        case 7:
            $message_res = SendToNetworkRepairApply($apply->apply_user_henau_openid,$message);
            break;
        case 8:
            $message_res = SendToMultimediaApply($apply->apply_user_henau_openid,$message);
            break;
        default:
            Error('非法请求！');
        }
        // $message_res = SendToVpnApply($apply->apply_user_henau_openid,$message);
        if($message_res->status == 'error')
            ErrorLog("向审批人发送通知失败");

        Success('审批成功！');
    }

    // 用户侧申请列表
    public function UserApplyDataList() {
        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        $jwt_token = input('post.jwt_token');

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number  = $user_info->user_number;

        $apply_list = Apply::field('apply_id,apply_app_name,apply_user_name,apply_time,apply_status,apply_attachment,remark')
            ->where('apply_user_number',$user_number)
            ->where('is_delete',0)
            ->order('apply_time', 'desc')
            ->select();

        if($apply_list) {
            PostResSuccess($apply_list);
        }
        else {
            Error('请求失败！');
        }
    }

    // 返回所有待批准的申请
    public function PendingApprovalList() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        $jwt_token = input('post.jwt_token');

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);
        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $approval_user_number    = $user_info->user_number;
        if($user_info->role_mask == 0)
            Error('当前用户没有查看权限！');

        $apply_list = Apply::field('apply_id,apply_app_name,apply_user_name,apply_user_number,apply_user_section,apply_time,apply_status,apply_attachment,apply_reason')
            ->where('is_delete',0)
            ->where('apply_status',0)
            ->order('apply_time', 'desc')
            ->where('app_approval_list','like',"%$approval_user_number%")
            ->select();

        if($apply_list) {
            PostResSuccess($apply_list);
        }
        else {
            Error('请求失败！');
        }
    }

    // 返回所有已审核的申请
    public function ApprovedList() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');
        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        $jwt_token = input('post.jwt_token');

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        if($user_info->role_mask == 0)
            Error('当前用户没有查看权限！');
        
        $approval_user_number    = $user_info->user_number;

        $apply_list = Apply::field('apply_id,apply_app_name,apply_user_name,apply_user_number,apply_user_section,apply_time,approval_user_name,apply_phone_number,apply_status,apply_attachment,apply_reason,remark')
            ->where('approval_user_number',$approval_user_number)
            ->where('apply_status','<>',0)
            ->where('is_delete',0)
            ->order('apply_time', 'desc')
            ->select();
        if($apply_list) {
            PostResSuccess($apply_list);
        }
        else {
            Error('请求失败！');
        }
    }

    // 用户侧搜索申请记录，支持类型模糊查询
    public function UserApplyInfoSearch() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        $jwt_token = input('post.jwt_token');

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        $search_data = input('post.search_data');
        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number  = $user_info->user_number;

        $data = Apply::field('apply_id,apply_app_name,apply_user_name,apply_time,apply_status,apply_attachment,apply_reason,remark')
            ->where('apply_user_number',$user_number)
            ->where('apply_app_name','like',"%$search_data%")
            ->order('apply_id', 'desc')
            ->where('is_delete',0)
            ->select();
        if($data) {
            PostResSuccess($data);
        }
        else {
            Error('请求失败！');
        }
    }

    // 管理侧搜索申请记录，支持模糊查询
    public function AdminUserApplySearch() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        $jwt_token = input('post.jwt_token');

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $approval_user_number    = $user_info->user_number;

        if($user_info->role_mask == 0)
            Error('当前用户没有查看权限！');

        $search_data = input('post.search_data');
        $data = Apply::field('apply_id,apply_app_name,apply_user_name,apply_user_number,apply_phone_number,apply_user_email,apply_time,apply_status,approval_user_name,remark,apply_attachment,apply_reason')
            ->where('apply_app_name|apply_user_number|apply_user_name|apply_user_section|apply_user_email|apply_phone_number|apply_reason','like',"%$search_data%")
            ->where('app_approval_list','like',"%$approval_user_number%")
            ->order('apply_time', 'desc')
            ->select();

        if($data) {
            PostResSuccess($data);
        }
        else {
            Error('请求失败！');
        }
    }

    // VPN申请提交信息接口
    public function VpnApply() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');
            
        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        // referer判断
        // if(Request::instance()->header('REFERER')) {
        //     return json('非法请求！',403);
        // }

        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        $phone          = input('post.apply_phone_number');
        $reason         = input('post.apply_reason');
        $user_section   = input('post.user_section');
        $jwt_token      = input('post.jwt_token');
        $grade          = input('post.grade');
            
        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);
        
        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number       = $user_info->user_number;

        $app_data = App::get(['app_id' => 2]);
        // if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
        //     Error('当前用户无申请权限！');
        if(Strlen($user_number) === 10) {
            // $stu_grade = strval(intval(date('Y')) - 4);
            // $stu_grade = substr($stu_grade,-2);
            // $res_num = substr(($user_number),0,2);
            // if($res_num != $stu_grade) {
                Error('当前用户无申请权限！');
            // }
        }
        
        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        // 院系/部门是否符合要求
        if($res = checkTextLength($user_section,0,15))
            Error("院系/部门".$res);

        $user_name         = $user_info->user_name;
        // $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;
        // 接收附件
        // $file = request()->file('image');

        // // 如果用户未上传附件
        // if(!$file)
        //     Error('请先上传申请表');

        // // 如果附件存在
        // // 验证文件是否合法
        // $validate = $this->validate(
        //     ['image' => $file],
        //     ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        // );
        // if (true !== $validate) {
        //     // 验证不通过，返回错误信息
        //     Error($validate);
        // }

        // // 移动附件到指定的目录
        // $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

        // if(!$info) 
        //     Error('请求失败，请稍后重试！');

        // // 获取上传文件的网络访问路径
        // $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());
        // 10位  本科生
        // 8-9位 研究生
        // 6-7位 教职工
        if(!empty($grade) && is_numeric($grade)) {
            $user_section  = $user_section ." ".$grade."级";
        }
        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();

        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => $reason,
            'apply_attachment'           => "",
            'apply_app_id'               => 2, // VPN申请
            'apply_app_name'             => $app_data->app_name,
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);

        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');
            
        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }

        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToVpnApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);

        if($res->status == 'error')
            ErrorLog("向VPN申请审批人发送通知失败");

        Success('提交成功！');
    }

    // IP地址申请信息接口
    public function IpAddressApply() {

        Error('暂不支持当前申请方式！');
        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        // referer判断
        // if(Request::instance()->header('REFERER')) {
        //     return json('非法请求！',403);
        // }

        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        $phone      = input('post.apply_phone_number');
        // $email      = input('post.apply_email');
        $reason     = input('post.apply_reason');
        $jwt_token  = input('post.jwt_token');

        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number       = $user_info->user_number;

        $app_data = App::get(['app_id' => 3]);
        if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
            Error('当前用户无申请权限！');
        
        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        // 驳回原因是否符合要求
        if($res = checkTextLength($reason,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("申请缘由".$res);

        // JWT 用户身份信息获取模块
        $user_name         = $user_info->user_name;
        $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;

        // 接收附件
        $file = request()->file('image');

        // 如果用户未上传附件
        if(!$file)
            Error('请先上传申请表');

        // 如果附件存在
        // 验证文件是否合法
        $validate = $this->validate(
            ['image' => $file],
            ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        );
        if (true !== $validate) {
            // 验证不通过，返回错误信息
            Error($validate);
        }

        // 移动附件到指定的目录
        $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

        if(!$info) 
            Error('请求失败，请稍后重试！');
       

        // 获取上传文件的网络访问路径
        $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());

        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();

        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => $reason,
            'apply_attachment'           => $save_name,
            'apply_app_id'               => 3, // IP申请
            'apply_app_name'             => $app_data->app_name,
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);

        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }

        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToIpAddressApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);
        if($res->status == 'error')
            ErrorLog("向IP地址申请审批人发送通知失败");

        Success('提交成功！');
    }

    // 服务器申请提交信息接口
    public function ServerApply() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');
    
        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }
    
        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }
    
        $phone      = input('post.apply_phone_number');
        // $email      = input('post.apply_email');
        $reason     = input('post.apply_reason');
        $jwt_token  = input('post.jwt_token');
    
        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }
    
        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');
    
        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);
            
        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number       = $user_info->user_number;

        $app_data = App::get(['app_id' => 4]);
        if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
            Error('当前用户无申请权限！');

        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        // 驳回原因是否符合要求
        if($res = checkTextLength($reason,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("申请缘由".$res);
    
        // JWT 用户身份信息获取模块
        $user_name         = $user_info->user_name;
        $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;
    
        // 接收附件
        $file = request()->file('image');
    
        // 如果用户未上传附件
        if(!$file)
            Error('请先上传申请表');
    
        // 如果附件存在
        // 验证文件是否合法
        $validate = $this->validate(
            ['image' => $file],
            ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        );
        if (true !== $validate) {
            // 验证不通过，返回错误信息
            Error($validate);
        }
    
        // 移动附件到指定的目录
        $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');
    
        if(!$info) 
            Error('请求失败，请稍后重试！');
           
    
        // 获取上传文件的网络访问路径
        $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());
    
        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();
    
        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => $reason,
            'apply_attachment'           => $save_name,
            'apply_app_id'               => 4, // 服务器申请
            'apply_app_name'             => $app_data->app_name,
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);
    
        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }
    
        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToServerApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);
        if($res->status == 'error')
            ErrorLog("向服务器申请审批人发送通知失败");

        Success('提交成功！');
    }

    // 域名申请提交信息接口
    public function DomainApply() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        // referer判断
        // if(Request::instance()->header('REFERER')) {
        //     return json('非法请求！',403);
        // }

        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        $phone      = input('post.apply_phone_number');
        // $email      = input('post.apply_email');
        $reason     = input('post.apply_reason');
        $jwt_token  = input('post.jwt_token');

        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);
        
        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number = $user_info->user_number;

        $app_data = App::get(['app_id' => 5]);
        if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
            Error('当前用户无申请权限！');

        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        // 驳回原因是否符合要求
        if($res = checkTextLength($reason,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("申请缘由".$res);

        // JWT 用户身份信息获取模块
        $user_name         = $user_info->user_name;
        $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;

        // 接收附件
        $file = request()->file('image');

        // 如果用户未上传附件
        if(!$file)
            Error('请先上传申请表');

        // 如果附件存在
        // 验证文件是否合法
        $validate = $this->validate(
            ['image' => $file],
            ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        );
        if (true !== $validate) {
            // 验证不通过，返回错误信息
            Error($validate);
        }

       // 移动附件到指定的目录
       $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

       if(!$info) 
           Error('请求失败，请稍后重试！');
       

       // 获取上传文件的网络访问路径
       $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());

        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();

        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => $reason,
            'apply_attachment'           => $save_name,
            'apply_app_id'               => 5, // 域名申请
            'apply_app_name'             => $app_data->app_name,
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);

        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }

        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToDomainApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);
        if($res->status == 'error')
            ErrorLog("向域名申请审批人发送通知失败");

        Success('提交成功！');
    }

    // 无线上网申请提交信息接口
    public function WIFIApply() {
        Error('暂不支持当前申请方式！');
        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        // referer判断
        // if(Request::instance()->header('REFERER')) {
        //     return json('非法请求！',403);
        // }

        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        $phone      = input('post.apply_phone_number');
        // $email      = input('post.apply_email');
        $reason     = input('post.apply_reason');
        $jwt_token  = input('post.jwt_token');

        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);
        
        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number = $user_info->user_number;

        $app_data = App::get(['app_id' => 6]);
        if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
            Error('当前用户无申请权限！');

        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        // 驳回原因是否符合要求
        if($res = checkTextLength($reason,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("申请缘由".$res);

        // JWT 用户身份信息获取模块
        $user_name         = $user_info->user_name;
        $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;

        // 接收附件
        $file = request()->file('image');

        // 如果用户未上传附件
        if(!$file)
            Error('请先上传申请表');

        // 如果附件存在
        // 验证文件是否合法
        $validate = $this->validate(
            ['image' => $file],
            ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        );
        if (true !== $validate) {
            // 验证不通过，返回错误信息
            Error($validate);
        }

       // 移动附件到指定的目录
       $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

       if(!$info) 
           Error('请求失败，请稍后重试！');
       

       // 获取上传文件的网络访问路径
       $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());

        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();

        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => $reason,
            'apply_attachment'           => $save_name,
            'apply_app_id'               => 6, // 无线上网申请
            'apply_app_name'             => $app_data->app_name,
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);

        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }

        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToWIFIApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);
        if($res->status == 'error')
            ErrorLog("向无线上网申请审批人发送通知失败");

        Success('提交成功！');
    }

    // 网络故障报修申请提交信息接口
    public function NetworkRepairApply() {

        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        // referer判断
        // if(Request::instance()->header('REFERER')) {
        //     return json('非法请求！',403);
        // }

        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        $phone          = input('post.apply_phone_number');
        $campus         = input('post.campus');
        $floor_number   = input('post.floor_number');
        $room_number    = input('post.room_number');
        $phone          = input('post.apply_phone_number');
        $reason         = input('post.apply_reason');
        $jwt_token      = input('post.jwt_token');

        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // JWT 用户身份信息获取模块
        $user_info    = $check_res['data']->user_info;
        $user_number  = $user_info->user_number;
        $app_data     = App::get(['app_id' => 7]);

        // 判断是否是22级
        // if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
        //     Error("非法请求！");

        // if(CheckUserType($user_number) == 1 && !CheckUserNum($user_number))
        //     Error("非法请求！");

        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        if($campus != "龙子湖校区" && $campus != "文化路校区") {
            Error('校区名称错误！');
        }

        // 申请原因是否符合要求
        if($res = checkTextLength($reason,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("申请缘由".$res);
        
        // 地点是否符合要求
        if($res = checkTextLength($floor_number,2,config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("楼号".$res);

        // 房间号是否符合要求
        if($res = checkTextLength($room_number,2,config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("房间号".$res);

        // JWT 用户身份信息获取模块
        $user_name         = $user_info->user_name;
        $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;

        // 接收附件
        $file = request()->file('image');

        // 如果用户未上传附件
        if(!$file)
            Error('请先上传申请表');

        // 如果附件存在
        // 验证文件是否合法
        $validate = $this->validate(
            ['image' => $file],
            ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        );
        if (true !== $validate) {
            // 验证不通过，返回错误信息
            Error($validate);
        }

       // 移动附件到指定的目录
       $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

       if(!$info) 
           Error('请求失败，请稍后重试！');
       

       // 获取上传文件的网络访问路径
       $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());

        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();

        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => "维修地点：".$campus." ".$floor_number.$room_number."，问题描述：". $reason,
            'apply_attachment'           => $save_name,
            'apply_app_id'               => 7, // 网络故障报修申请
            'apply_app_name'             => $app_data->app_name,
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);

        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }

        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToNetworkRepairApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);
        if($res->status == 'error')
            ErrorLog("向网络故障报修申请审批人发送通知失败");

        Success('提交成功！');
    }

    // 多媒体申请提交信息接口
    public function MultimediaApply() {
        // 判断是否是post请求
        if (!Request::instance()->isPost())
            Error('非法请求！');

        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        $ua = Request::instance()->header('user-agent'); // 获取用户的User-Agent信息
        if ($true_ip === 'null' || !isValidUA($ua)) { // 如果获取的用户ip地址为'null'或者User-Agent信息非法
            Error('非法请求！'); // 返回非法访问的错误信息
        }

        // referer判断
        // if(Request::instance()->header('REFERER')) {
        //     return json('非法请求！',403);
        // }

        // 对请求频次进行限制
        $data = RequestRateLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM')); 
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        $phone      = input('post.apply_phone_number');
        // $email      = input('post.apply_email');
        $reason     = input('post.apply_reason');
        $jwt_token  = input('post.jwt_token');

        // 判断是否为非法格式数据
        if (is_array($reason) || is_array($jwt_token)) {
            Error('非法请求！');
        }

        // 检验jwt令牌格式是否正确
        // if(!CheckJwtToken($jwt_token))
        //     Error('非法请求！');

        // JWT 用户验证模块
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0 )
            Error($check_res['msg']);

        // JWT 用户身份信息获取模块
        $user_info = $check_res['data']->user_info;
        $user_number       = $user_info->user_number;

        $app_data = App::get(['app_id' => 8]);
        if(!(CheckUserType($user_number) & $app_data->app_apply_user_type))
            Error('当前用户无申请权限！');

        // 检验手机号格式是否正确
        if(!IsPhoneNum($phone)) {
            Error('手机号格式错误');
        }

        // 驳回原因是否符合要求
        if($res = checkTextLength($reason,config('APPLY_REASON_TEXT_MIN_LENGTH'),config('APPLY_REASON_TEXT_MAX_LENGTH')))
            Error("申请缘由".$res);
        // JWT 用户身份信息获取模块
        $user_name         = $user_info->user_name;
        $user_section      = $user_info->user_section;
        $user_henau_openid = $user_info->user_henau_openid;

        // 接收附件
        $file = request()->file('image');

        // 如果用户未上传附件
        if(!$file)
            Error('请先上传申请表');

        // 验证文件是否合法
        $validate = $this->validate(
            ['image' => $file],
            ['image' => 'require|file|image|fileSize:'.config('ALLOW_IMAGE_MAX').'|fileExt:'.config('ALLOW_IMAGE_TYPE')]
        );
        if (true !== $validate) {
            // 验证不通过，返回错误信息
            Error($validate);
        }
        // 移动附件到指定的目录
        $info = $file->move(ROOT_PATH . 'public' . DS . 'uploads');

        if(!$info) 
            Error('请求失败，请稍后重试！');

        // 获取上传文件的网络访问路径
        $save_name = str_replace("\\", "/", config('SERVER_ADDRESS') .'/uploads'. $info->getSaveName());
        // 申请数量增加 1
        $app_approval_list = $app_data->app_approval_list;
        $app_data->app_apply_num = $app_data->app_apply_num + 1;
        $app_data->save();
        $info = new Apply([
            'apply_user_henau_openid'    => $user_henau_openid,
            'apply_user_name'            => $user_name,
            'apply_user_number'          => $user_number,
            'apply_user_section'         => $user_section,
            // 'apply_user_email'           => $email,
            'apply_phone_number'         => $phone,
            'apply_reason'               => $reason,
            'apply_attachment'           => $save_name,
            'apply_app_id'               => 8, // 多媒体申请
            'apply_app_name'             => $app_data->app_name,
            'app_approval_list'          => $app_approval_list,
            'approval_stage'             => 0,
            'apply_time'                 => date('Y-m-d H:i:s', time()),
            'apply_status'               => 0,
            'apply_user_ip_info'         => $true_ip,
            'apply_user_ua_info'         => $ua,
            // 'apply_evidence_save_name'   => $save_name,
        ]);

        $res = $info->save();
        if(!$res)
            Error('请求失败，请稍后重试！');

        $app_approval_list = json_decode($app_approval_list);
        foreach ($app_approval_list as $row) {
            foreach ($row as $user_number) {
                $app_approval_user_number[] = $user_number;
            }
        }

        $approval_user_henau_opneid = User::where('number',$app_approval_user_number[0])->value('henau_openid');

        $res = SendToMultimediaApproval($approval_user_henau_opneid, SectionChange($user_section)." ".$user_name ,$phone);
        if($res->status == 'error')
            ErrorLog("向多媒体申请审批人发送通知失败");

        Success('提交成功！');
    }
}