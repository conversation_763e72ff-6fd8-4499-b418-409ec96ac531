//正则判断用户填写信息是否规范
const regMsg = (data) => {
  const info = {};
  for (const key in data) {
    info[data[key].name] = data[key].value;
  }
  let errmsg = `请`;
  let nameErr = false , numErr = false , phoneErr = false;
  //判断数据是否都填写了
  if(info.user_name === '' || info.user_number === '' || info.user_phonenumber === ''|| info.user_ideas === '')
    errmsg = '请正确填写每一项信息后提交';
  //其他情况
  else {
    if(!(/^[\u4E00-\u9FA5]{2,}$/.test(info.user_name))) {
      errmsg += '正确填写姓名';
      nameErr = true;
    }
    //判断学工号是否合法
    if(!(/^[0-9](([0-9]{5})|([0-9]{7})|([0-9]{8})|([0-9]{9}))$/.test(info.user_number))) {
      if(nameErr)
        errmsg += '、学工号 ';
      else
        errmsg += '正确填写学工号 ';
      numErr = true;
    } 
    //判断手机号是否合法
    if(!(/^[1]([3-9])[0-9]{9}$/.test(info.user_phonenumber))) {
      if(nameErr || numErr)
        errmsg += '、联系电话';
      else 
        errmsg += '正确填写联系电话';
      phoneErr = true;
    }
    if(info.user_ideas.match(/^[ ]*$/)) {
      if(nameErr || numErr || phoneErr)
        errmsg += '、想法或建议';
      else
        errmsg += '正确填写想法或建议'
    }
  }
  //返回处理结果
  if(errmsg !== '请')
    return errmsg;
  else
    return true;
};
//过滤敏感信息函数，传入正则表达式以及需要判断数据
const clearDanger = (pattern,data)=>{
  let ans = '';
  for(let i = 0; i< data.length; i++) {
    ans += data.substr(i,1).replace(pattern,'')
  }
  return ans;
}
const submitInfo = (loadTime,ideaMsg)=>{
      //时间以秒为单位
      const user_take_time = parseInt((Date.now() - loadTime) / 1000);
      //获取用户信息
      const infoCollect = {};
      for (const key in ideaMsg) {
        infoCollect[ideaMsg[key].name] = ideaMsg[key].value;
      }
      //存储用户浏览网页时间
      infoCollect['user_take_time'] = user_take_time;
      var pattern = new RegExp("[`$^*=|,'\\[\\].<>/?~@#&*——|{}]");
      let rawMsg = infoCollect['user_ideas'];
      //正则处理函数过滤敏感字符
      const ans = clearDanger(pattern,rawMsg);
      infoCollect['user_ideas'] = ans;
      //提交表单数据
      $.ajax({
        type: "POST",
        url: "SaveData.html",
        data: infoCollect,
        dataType: "json",
        success(res) {
          // 提交成功
          if(res.code == 1) {
            // 清空表单内容
            document.getElementById('submit_idea').reset();
            layer.alert('提交成功，感谢你的参与！',function() {
              if(document.referrer) {
                window.history.go(-1);
              } else {
                //referer为空重定向到微服务首页
                window.location = "MicroService.html";
              }
            });
          // 提交失败
          } else {
            layer.alert(res.msg);
          }
        },
        error(e) {
          layer.alert('提交失败，请刷新页面后重试！');
        }
  });
}
//es6抛出函数
export{
  regMsg,
  submitInfo
}