﻿@media screen and (min-width: 1200px){
    .layui-container {
        width: 1170px;
    }
    .layui-btn {
        width: 150px;
    }
}
@media screen and (min-width: 992px){
    .layui-container {
        width: 970px;
    }
}
@media screen and (min-width: 768px){
    .layui-container {
        width: 750px;
    }
}
@media screen and (min-width: 500px){
    .layui-btn {
        width: 120px;
    }
}
/* homeadjustdark */
@media (prefers-color-scheme: dark) { 
    body { 
      background: rgb(33 33 33 )!important;
    }
    .layui-input{
        background-color: rgb(50 50 50)!important;
    }
    article{
      color:rgba(255,255,255,.86);
    }
    .layui-elem-field legend{
        color:#f3f3f3;
    }
    .layui-form-label{
        color: rgb(210 210 210);
    }
    .layui-form-pane .layui-form-label{
        background-color: rgb(40 40 40)!important;
    }
    .layui-input, .layui-select, .layui-textarea{
        background-color: #474545;
    }
    .layui-input{
        border-color: #7e7a7ac4!important;
    }
    .layui-input, .layui-select, .layui-textarea{
        background-color: rgb(50 50 50)!important;
    }
    button, input, optgroup, option, select, textarea{
        color: #cecbcb!important;
    }
    .layui-badge-rim, .layui-border, .layui-colla-content, .layui-colla-item, .layui-collapse, .layui-elem-field, .layui-form-pane .layui-form-item[pane], .layui-form-pane .layui-form-label, .layui-input, .layui-layedit, .layui-layedit-tool, .layui-panel, .layui-quote-nm, .layui-select, .layui-tab-bar, .layui-tab-card, .layui-tab-title, .layui-tab-title .layui-this:after, .layui-textarea{
        border-color: #575656!important;
    }
    .layui-btn{
        border-color: #5b5f67!important;
    }
    
    .layui-form-select dl dd.layui-this{
        background-color: #2f2d2d!important;
        color: #e2e2e2;
    }
    .layui-form-select dl{
        background-color: #4f4f4f!important;
        border: 1px solid #a6a0a0;
    }
    .layui-form-select dl dd, .layui-form-select dl dt{
        background-color: #3d3d3d!important;
        color: #d2cece!important;
    }
   
    .layui-laydate-content th {
        font-weight: 400;
        color: #d6d6d6!important;
    }
    .layui-laydate .laydate-disabled, .layui-laydate .laydate-disabled:hover{
        color:  #808080a8!important;
    }
    .layui-progress{
        background-color:#898484!important;
    }
    .layui-laydate, .layui-laydate-hint{
        background-color: #333!important;
        color: #e4e4e4!important;
        border: 1px solid hsl(0, 0%, 44%)!important;
    }
    .layui-layer-dialog{
        background-color: #323030!important
    }
    .layui-layer-title{
        color: #d8d8d8!important;
    }

    .layui-input:hover{
        border-color: #bdbaba!important;
    } 
    .layui-laydate-footer span{
        background-color: #676767!important;
        /* border: 1px solid #443e3e; */
        border: 0.4px solid #c4c3c3bd!important;
        padding: 0 10px;
    }
    .layui-laydate-content td{
        color: #c1c1c1!important;
    }
    .layui-laydate-content .laydate-day-next, .layui-laydate-content .laydate-day-prev {
        color: #686666!important;
    }
    .layui-laydate-list,.layui-laydate-list{
        background-color: #323030!important;
    }
    .laydate-time-list ol li{
        color: #cecbcb!important;
    }
    .laydate-time-list p{
        color: #cecbcb!important;
    }
    .layui-laydate-list li:hover {
        color: rgb(15, 15, 15);
    }
    /* .layui-layer-ico{
        background:url(dark_icon.png) no-repeat
    } */
     .layui-layer-content{
        background-color: #323030!important;
        color: #d8d8d8!important;
    }

   
}
/* 隐藏问题选择第一条内容 */
.layui-form-select dl dd.layui-select-tips{
    display: none;
}

/* 白昼模式黑框 */
/* .layui-badge-rim, .layui-border,.layui-btn, .layui-colla-content, .layui-colla-item, .layui-collapse, .layui-elem-field, .layui-form-pane .layui-form-item[pane], .layui-form-pane .layui-form-label, .layui-input, .layui-layedit, .layui-layedit-tool, .layui-panel, .layui-quote-nm, .layui-select, .layui-tab-bar, .layui-tab-card, .layui-tab-title, .layui-tab-title .layui-this:after, .layui-textarea{

    border-color: #828282c4!important;
   
} */

.layui-form-pane .layui-form-label{
    background-color: #f0f0f0;
}
/* .layui-input:hover{
    border-color: #c1bebe!important;
}  */
.layui-btn{
    border-radius: 2mm 2mm 2mm 2mm;
} 

.layui-form-item .layui-form-label{
    border-radius: 2mm 0 0 2mm;
}
.layui-input-block , button{
    border-radius: 0 2mm 2mm 0;
    
}
#submit,#reset{
    border-radius: 2mm 2mm 2mm 2mm;
}
.layui-laydate-footer span{
    padding: 0 10px;
}

/* 夜间模式 */
/* .darkMode body { 
  background: rgb(33 33 33 )!important;
}
.darkMode article{
  color:rgba(255,255,255,.86);
}
.darkMode .layui-field-title{
    color:rgba(255,255,255,.86);
}
.darkMode .layui-form-pane .layui-form-label{
    background-color: rgb(40 40 40);
    color: rgb(210 210 210);
}

.darkMode .layui-input{

    border-color: #7e7c7c!important;
    color: #dfdada!important;
    background-color: rgb(50 50 50)!important;

}
.darkMode .layui-btn-primary{
    border-color: #8d8b8b;
    color: #ababab;
}
.darkMode .layui-laydate, .layui-laydate-hint{
    background-color: #333!important;
    color: #e4e4e4!important;
    border: 1px solid #706f6f!important;
}
.darkMode .layui-laydate-footer span{
    background-color: #676767!important;
    border: 1px solid #443e3e;
}
.darkMode .layui-laydate-content td{
    color: #c1c1c1!important;
}
.darkMode .layui-laydate-content .laydate-day-next, .layui-laydate-content .laydate-day-prev {
    color: #686666!important;
}

.darkMode .layui-progress{
    background-color:#898484!important;
}
.darkMode .layui-form-select dl dd, .layui-form-select dl dt{
    background-color: #3d3d3d!important;
    color: #d2cece!important;
}

.darkMode .layui-form-select dl dd.layui-this{
    background-color: #2f2d2d!important;
    color: #e2e2e2;
}
.darkMode .layui-form-select dl{
    background-color: #4f4f4f!important;
    border: 1px solid #a6a0a0;
} */
/* 移动端时间选择模块 时分秒不能滑动bug修复 */
.laydate-time-list ol{
    overflow-x: hidden !important;
    overflow-y:auto !important;
}