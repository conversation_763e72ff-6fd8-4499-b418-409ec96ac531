<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

// 应用公共文件
// 发送给电子邮箱申请审批人
function SendToEmailApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_EMAIL_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给VPN申请人
function SendToEmailApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_EMAIL_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给VPN申请审批人
function SendToVpnApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_VPN_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给IP地址申请审批人
function SendToVpnApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_VPN_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给IP地址申请审批人
function SendToIpAddressApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_IP_ADDRESS_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给IP地址申请审批人
function SendToIpAddressApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_IP_ADDRESS_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}


// 发送给服务器申请审批人
function SendToServerApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_SERVER_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给服务器申请审批人
function SendToServerApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_SERVER_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给域名申请审批人
function SendToDomainApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_DOMAIN_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给域名申请审批人
function SendToDomainApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_DOMAIN_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}


// 发送给域名申请审批人
function SendToWIFIApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_WIFI_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给域名申请审批人
function SendToWIFIApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_WIFI_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给网络报修申请审批人
function SendToNetworkRepairApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_NETWORK_REPAIR_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给网络保修申请审批人
function SendToNetworkRepairApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_NETWORK_REPAIR_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给域名申请审批人
function SendToMultimediaApproval($henau_openid, $keyword1 = "", $keyword2 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_MULTIMEDIA_APPROVAL'),
        "url" => config('MESSAGE_TO_APPROVAL_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1 // 格式为: 信管学院 学生一
                ],
            "keyword2" =>  [
                "value" => $keyword2
            ]
        ]
    ];
    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 发送给域名申请审批人
function SendToMultimediaApply($henau_openid, $keyword1 = "") {
    $massage_data = [
        "henau_openid" => $henau_openid,
        "template_number" => config('SEND_TO_MULTIMEDIA_APPLY'),
        "url" => config('MESSAGE_TO_APPLY_URL'),
        "data" => [
            "keyword1" => [
                "value" => $keyword1
                ]
        ]
    ];

    return json_decode(HttpPost(config('WXMESSAGE_API')."appid=".config('AppID')."&secret=".config('AppSecret'),json_encode($massage_data)));
}

// 检验用户类型
function CheckUserType($user_number) {
    if(Strlen($user_number) == 10) {
        return 1;
    } else if(Strlen($user_number) < 10 && Strlen($user_number) >= 8) {
        return 2;
    } else if(Strlen($user_number) < 8) {
        return 4;
    } else {
        return 0;
    }
}