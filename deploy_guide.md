# 🚀 微服务后端项目 - phpStudy部署指南

## 📋 项目信息
- **框架**: ThinkPHP 5.0
- **PHP版本要求**: >= 5.4.0 (推荐 7.2+)
- **数据库**: MySQL 5.7+

## 🛠️ 部署步骤

### 1. phpStudy环境配置

#### 启动服务
1. 打开phpStudy
2. 启动 **Apache** (或 Nginx)
3. 启动 **MySQL**
4. 确保服务状态为绿色

#### 设置虚拟主机（推荐）
1. 点击"网站" → "创建网站"
2. 配置信息：
   - **域名**: `microservice.local`
   - **端口**: `80`
   - **根目录**: `D:\phpstudy_pro\WWW\microservice-dev-pc-ls\public`
   - **PHP版本**: 7.2+

#### 修改hosts文件（如果使用虚拟主机）
在 `C:\Windows\System32\drivers\etc\hosts` 文件末尾添加：
```
127.0.0.1 microservice.local
```

### 2. 数据库配置

#### 创建数据库
1. 打开phpMyAdmin (phpStudy → 数据库 → 管理)
2. 创建数据库：
```sql
CREATE DATABASE db_wfw_240916 CHARACTER SET utf8 COLLATE utf8_general_ci;
CREATE DATABASE db_itcservice CHARACTER SET utf8 COLLATE utf8_general_ci;
```

#### 导入数据库结构
1. 选择 `db_wfw_240916` 数据库
2. 点击"导入" → 选择 `sql/db_wfw.sql`
3. 选择 `db_itcservice` 数据库  
4. 点击"导入" → 选择 `sql/db_itcservice.sql`

### 3. 项目配置

#### 文件权限设置
确保以下目录可写：
- `runtime/` (运行时缓存目录)
- `public/uploads/` (如果有文件上传功能)

#### 数据库连接配置
已自动配置为phpStudy默认设置：
- 主机: `127.0.0.1:3306`
- 用户名: `root`
- 密码: `root`

### 4. 访问测试

#### 方式1：虚拟主机访问
```
http://microservice.local
```

#### 方式2：直接访问
```
http://localhost/microservice-dev-pc-ls/public
```

#### 方式3：PHP内置服务器
在项目根目录执行：
```bash
cd public
php -S localhost:8888
```
然后访问: `http://localhost:8888`

## 🔧 API接口测试

### 主要接口列表

#### 微服务核心接口
- 获取用户信息: `POST /WfwGetUserInfo`
- 获取配置信息: `POST /WfwGetConfigInfo`
- 获取公告信息: `POST /WfwGetNoticeInfo`
- 服务访问统计: `POST /WfwCollectServiceVisitCount`

#### 资源申请接口
- 邮箱申请: `POST /EmailApply`
- VPN申请: `POST /VpnApply`
- IP申请: `POST /IpAddressApply`
- 服务器申请: `POST /ServerApply`

#### 测试示例
```bash
# 使用curl测试
curl -X POST http://microservice.local/WfwGetConfigInfo \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

## 🐛 常见问题

### 1. 404错误
- 检查Apache mod_rewrite模块是否启用
- 确认.htaccess文件存在于public目录

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库用户名密码正确
- 检查数据库是否存在

### 3. 权限错误
- 确保runtime目录可写
- Windows下可能需要给予完全控制权限

### 4. PHP扩展缺失
确保以下PHP扩展已启用：
- `pdo_mysql`
- `mbstring`
- `openssl`
- `curl`

## 📁 项目结构

```
microservice-dev-pc-ls/
├── application/          # 应用目录
│   ├── wfw/             # 微服务核心模块
│   ├── apply/           # 资源申请模块
│   ├── getmac/          # MAC地址统计
│   ├── itwelcome/       # IT工作室报名
│   ├── config.php       # 全局配置
│   ├── database.php     # 数据库配置
│   └── route.php        # 路由配置
├── public/              # Web根目录
│   ├── index.php        # 入口文件
│   └── .htaccess        # Apache重写规则
├── sql/                 # 数据库文件
├── runtime/             # 运行时目录
└── vendor/              # 依赖包
```

## 🎯 下一步

1. 测试基本接口是否正常
2. 根据需要配置具体的业务参数
3. 设置定时任务（如果需要）
4. 配置日志记录
5. 设置错误监控

## 📞 技术支持

如遇问题，请检查：
1. phpStudy错误日志
2. Apache/Nginx错误日志  
3. PHP错误日志
4. 项目runtime/log目录下的日志文件
