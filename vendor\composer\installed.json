{"packages": [{"name": "firebase/php-jwt", "version": "v6.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2023-02-09T21:01:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.4.0"}, "install-path": "../firebase/php-jwt"}, {"name": "topthink/framework", "version": "v5.0.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "643c58ed1bd22a2823ce5e95b3b68a5075f9087c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/643c58ed1bd22a2823ce5e95b3b68a5075f9087c", "reference": "643c58ed1bd22a2823ce5e95b3b68a5075f9087c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "topthink/think-installer": "~1.0"}, "require-dev": {"johnkary/phpunit-speedtrap": "^1.0", "mikey179/vfsstream": "~1.6", "phpdocumentor/reflection-docblock": "^2.0", "phploc/phploc": "2.*", "phpunit/phpunit": "4.8.*", "sebastian/phpcpd": "2.*"}, "time": "2022-10-25T14:59:38+00:00", "type": "think-framework", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "library/think"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the new thinkphp framework", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/v5.0.25"}, "install-path": "../topthink/framework"}, {"name": "topthink/think-installer", "version": "v1.0.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-installer.git", "reference": "eae1740ac264a55c06134b6685dfb9f837d004d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-installer/zipball/eae1740ac264a55c06134b6685dfb9f837d004d1", "reference": "eae1740ac264a55c06134b6685dfb9f837d004d1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"composer-plugin-api": "^1.0||^2.0"}, "require-dev": {"composer/composer": "^1.0||^2.0"}, "time": "2021-03-25T08:34:02+00:00", "type": "composer-plugin", "extra": {"class": "think\\composer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"think\\composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/top-think/think-installer/issues", "source": "https://github.com/top-think/think-installer/tree/v1.0.14"}, "install-path": "../topthink/think-installer"}], "dev": true, "dev-package-names": []}