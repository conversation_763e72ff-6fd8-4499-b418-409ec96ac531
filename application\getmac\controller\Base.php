<?php

namespace app\getmac\controller;

use think\Controller;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use app\apply\model\User;
use think\Request;

class Base extends Controller
{
    // 生成jwt密钥
    protected function CreatJwtToken($user_info) {
        $payload = [
            'iss' => config('SYSTEM_SUBORDINATE_UNITS'),           //签发人
            'exp' => time() + config('JWT_SECRET_EXPIRE_TIME'),    //过期时间
            'nbf' => time(),                                       //生效时间
            'user_info' => $user_info,                             //用户信息
        ];
        $token = JWT::encode($payload, config('JWT_SECRET'), 'HS256');
        return $token;
    }

    // 检验并解密jwt令牌
    protected function CheckJwtToken($jwt_token) {
        try {
            if(empty($jwt_token))
                Error('非法请求！');
            // 尝试验证JWT
            $info = JWT::decode($jwt_token, new Key(config('JWT_SECRET'), 'HS256'));
            $res['status'] = 1;
            $res['data'] = $info;
            return $res;
        } catch (\Exception $e) {
            // 如果捕获到ExpiredException，表示JWT已经过期
            // 可以输出异常信息，或者返回错误码，或者刷新JWT等

            // 过期时需要将其重定向到首页
            $res['status'] = 0;
            $res['msg'] = $e->getMessage();
            return $res;
        }
    }
}
