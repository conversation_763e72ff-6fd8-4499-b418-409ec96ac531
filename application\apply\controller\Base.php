<?php

namespace app\apply\controller;

use think\Controller;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use app\apply\model\User;
use think\Request;

class Base extends Controller
{
    // 生成jwt密钥
    protected function CreatJwtToken($user_info) {
        $payload = [
            'iss' => config('SYSTEM_SUBORDINATE_UNITS'),           //签发人
            'exp' => time() + config('JWT_SECRET_EXPIRE_TIME'),    //过期时间
            'nbf' => time(),                                       //生效时间
            'user_info' => $user_info,                             //用户信息
        ];
        $token = JWT::encode($payload, config('JWT_SECRET'), 'HS256');
        return $token;
    }

    // 检验并解密jwt令牌
    protected function CheckJwtToken($jwt_token) {
        try {
            if(empty($jwt_token))
                Error('非法请求！');
            // 尝试验证JWT
            $info = JWT::decode($jwt_token, new Key(config('JWT_SECRET'), 'HS256'));
            $res['status'] = 1;
            $res['data'] = $info;
            return $res;
        } catch (\Exception $e) {
            // 如果捕获到ExpiredException，表示JWT已经过期
            // 可以输出异常信息，或者返回错误码，或者刷新JWT等

            // 过期时需要将其重定向到首页
            $res['status'] = 0;
            $res['msg'] = $e->getMessage();
            return $res;
        }
    }

    // 定义函数名为 getUserInfo，用于获取用户身份信息
    protected function UserInfo() {
        
        // 检查用户ip和ua
        $true_ip = GetReverseProxyIP(); // 获取反向代理后的用户ip地址
        // 获取用户的User-Agent信息
        $ua = Request::instance()->header('user-agent');
        // 如果获取的用户ip地址为'null'或者User-Agent信息非法 
        if ($true_ip === 'null' || !isValidUA($ua)) {
            // 返回非法访问的错误信息
            Error('非法请求！');
        }

        // 从 GET 请求中获取 code 参数
        $code = input('get.code');

        // 对于未传code参数的请求进行拦截
        if(!$code) {
            Error('非法请求！');
        }

        // 对于格式不正确的code，禁止其访问
        if(!CheckCode($code))
            Error("错误的code格式");

        // 通过传入的 code 参数，调用 httpGet 函数获取 access_token
        $data = json_decode(httpGet(config('GET_ACCESS_TOKEN_API') . '?appid=' . config('AppID') . '&secret=' . config('AppSecret') . '&code=' . $code . '&grant_type=authorization_code'));

        // 判断是否成功获取access_token
        if(!$data || $data && $data->status != "success") {
            Error($data->data);
        }

        // 从对象中获取 access_token 和 henau_openid
        $data = $data->data;
        // 获取的access_token
        $access_token = $data->access_token;
        // 获取的用户身份唯一标识符
        $henau_openid = $data->henau_openid;

        // 通过 access_token 和 henau_openid换取用户身份信息
        $user_info = json_decode(httpGet(config('GET_USER_INFO_API') . '?access_token=' .$access_token . '&henau_openid=' . $henau_openid))->data;

        // 如果请求返回为空或请求状态不为success则返回报错
        if(!$user_info || $user_info && $user_info->status != 'success') {
            Error($data->data);
        }
        // 从获取的用户身份信息中获取用户的学号、姓名、学院，并保存到数组 $user 中
        // 用户学 / 工号
        $user_number = $user_info->user_number;
        // 用户姓名
        $user_name = $user_info->user_name; 
        // 用户所在学院或部门
        $user_section = $user_info->user_section;

        // 在数据库中查找用户信息，并将用户的角色信息保存到 $user 数组中
        $user = User::get(['number' => $user_number]);

        // 判断是否有审批权限
        if($user->role_mask & config('EMAIL_APPLY')) {
            $res['user_role'] = 0;
        }
        else {
            $res['user_role'] = 1;
        }

        $res['user_name'] = $user_name;
        $res['user_section'] = $user_section;

        $token = $this->CreatJwtToken($res);

        $data = ['token'     => $token,
                 'user_info' => $res
                ];

        PostResSuccess($data);
    }
}
