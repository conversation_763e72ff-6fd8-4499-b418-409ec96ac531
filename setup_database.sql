-- =====================================================
-- 微服务后端项目数据库初始化脚本
-- 适用于phpStudy环境
-- =====================================================

-- 创建主数据库
CREATE DATABASE IF NOT EXISTS db_wfw_240916 CHARACTER SET utf8 COLLATE utf8_general_ci;

-- 创建资源申请数据库
CREATE DATABASE IF NOT EXISTS db_itcservice CHARACTER SET utf8 COLLATE utf8_general_ci;

-- 显示创建结果
SHOW DATABASES LIKE 'db_%';

-- 使用说明
-- 1. 在phpMyAdmin中执行此脚本创建数据库
-- 2. 然后分别导入对应的SQL文件：
--    - db_wfw_240916 数据库导入 sql/db_wfw.sql
--    - db_itcservice 数据库导入 sql/db_itcservice.sql
-- 3. 如果需要创建专用用户，可以执行以下命令：

/*
-- 创建专用数据库用户（可选）
CREATE USER 'microservice_user'@'localhost' IDENTIFIED BY 'microservice_pass';
GRANT ALL PRIVILEGES ON db_wfw_240916.* TO 'microservice_user'@'localhost';
GRANT ALL PRIVILEGES ON db_itcservice.* TO 'microservice_user'@'localhost';
FLUSH PRIVILEGES;
*/
