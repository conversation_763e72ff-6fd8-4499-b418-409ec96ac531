-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-28 22:35:37
-- 服务器版本： 5.7.43-log
-- PHP 版本： 7.3.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_notice_create`
--

CREATE TABLE `table_notice_create` (
  `notice_id` int(10) NOT NULL COMMENT '公告ID',
  `notice_creat_time` datetime DEFAULT NULL COMMENT '公告创建时间',
  `notice_creat_user_number` char(15) DEFAULT NULL COMMENT '公告发布者学工号',
  `notice_title` varchar(50) DEFAULT NULL COMMENT '公告标题',
  `notice_name` varchar(50) DEFAULT NULL,
  `notice_text` text COMMENT '公告内容',
  `notice_views` int(50) NOT NULL COMMENT '公告查看人数',
  `notice_creat_user_ip` varchar(100) DEFAULT NULL COMMENT '公告发布者IP',
  `notice_creat_user_ua` varchar(700) DEFAULT NULL COMMENT '公告发布者UA',
  `soft_delete` tinyint(1) DEFAULT NULL COMMENT '软删除'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_notice_create`
--

INSERT INTO `table_notice_create` (`notice_id`, `notice_creat_time`, `notice_creat_user_number`, `notice_title`, `notice_name`, `notice_text`, `notice_views`, `notice_creat_user_ip`, `notice_creat_user_ua`, `soft_delete`) VALUES
(1, '2022-05-30 12:19:25', NULL, 'NoticeWfw', '微服务平台上线了', NULL, 5, NULL, NULL, 0),
(2, '2022-11-09 12:19:25', NULL, 'NoticeIdea', '“灵感小站”上线啦，快投递你的好想法吧！', NULL, 3, NULL, NULL, 0),
(3, '2022-12-12 12:20:04', NULL, 'NoticeDark', '追寻你的偏好：深色模式适配', NULL, 4, NULL, NULL, 0),
(4, '2023-03-02 12:20:04', NULL, 'NoticeWeappClassify', '引入微信小程序并对服务进行分类', NULL, 3, NULL, NULL, 0),
(5, '2023-09-01 12:20:04', NULL, 'NoticeUpdatePlatform', '不了解微服务？快来看看吧', NULL, 4, NULL, NULL, 0),
(6, '2023-11-20 23:03:20', NULL, 'NoticeWfwPlus', '微服务升级啦，全新平台等你来体验！', NULL, 8, NULL, NULL, 0);

--
-- 转储表的索引
--

--
-- 表的索引 `table_notice_create`
--
ALTER TABLE `table_notice_create`
  ADD PRIMARY KEY (`notice_id`) USING BTREE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
