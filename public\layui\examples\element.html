<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
<title>常用元素 - layui</title>

<link rel="stylesheet" href="../src/css/layui.css">

<style>
body{padding:20px;}
</style>
</head>
<body>

<ul class="layui-timeline">
  <li class="layui-timeline-item">
    <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
    <div class="layui-timeline-content layui-text">
      <h2 class="layui-timeline-title">2.0.0</h2>
      <p>杜甫的思想核心是儒家的仁政思想，他有“致君尧舜上，<em>再使风俗淳”的宏伟抱负</em>。杜甫虽然在世时名声并不显赫，但后来声名</p>
      <ul>
        <li>思想</li>
        <li>虽然在</li>
      </ul>
      哈哈哈
    </div>
  </li>
  <li class="layui-timeline-item">
    <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
    <div class="layui-timeline-content layui-text">
      <h2 class="layui-timeline-title">1.0.9</h2>
      哈哈哈
    </div>
  </li>
  <li class="layui-timeline-item">
    <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
    <div class="layui-timeline-content layui-text">
      <div class="layui-timeline-title">标题</div>
      内容
    </div>
  </li>
</ul>

<hr>

徽章：

<span class="layui-badge-dot"></span>
<span class="layui-badge-dot layui-bg-orange"></span>
<span class="layui-badge-dot layui-bg-green"></span>
<span class="layui-badge-dot layui-bg-cyan"></span>
<span class="layui-badge-dot layui-bg-blue"></span>
<span class="layui-badge-dot layui-bg-black"></span>
<span class="layui-badge-dot layui-bg-gray"></span>

<span class="layui-badge">6</span>
<span class="layui-badge">99</span>
<span class="layui-badge">61728</span>

<span class="layui-badge">赤</span>
<span class="layui-badge layui-bg-orange">橙</span>
<span class="layui-badge layui-bg-green">绿</span>
<span class="layui-badge layui-bg-cyan">青</span>
<span class="layui-badge layui-bg-blue">蓝</span>
<span class="layui-badge layui-bg-black">黑</span>
<span class="layui-badge layui-bg-gray">灰</span>

<span class="layui-badge-rim">Hot</span>

<hr>

<div class="layui-collapse" lay-filter="test" lay-accordion>
  <div class="layui-colla-item">
    <h2 class="layui-colla-title">杜甫</h2>
    <div class="layui-colla-content layui-show">
      <p>杜甫的思想核心是儒家的仁政思想，他有“致君尧舜上，再使风俗淳”的宏伟抱负。杜甫虽然在世时名声并不显赫，但后来声名远播，对中国文学和日本文学都产生了深远的影响。杜甫共有约1500首诗歌被保留了下来，大多集于《杜工部集》。</p>
    </div>
  </div>
  <div class="layui-colla-item">
    <h2 class="layui-colla-title">李清照</h2>
    <div class="layui-colla-content">
      <p>李清照出生于书香门第，早期生活优裕，其父李格非藏书甚富，她小时候就在良好的家庭环境中打下文学基础。出嫁后与夫赵明诚共同致力于书画金石的搜集整理。金兵入据中原时，流寓南方，境遇孤苦。所作词，前期多写其悠闲生活，后期多悲叹身世，情调感伤。形式上善用白描手法，自辟途径，语言清丽。</p>
    </div>
  </div>
  <div class="layui-colla-item">
    <h2 class="layui-colla-title">鲁迅</h2>
    <div class="layui-colla-content">
      <p>鲁迅一生在文学创作、文学批评、思想研究、文学史研究、翻译、美术理论引进、基础科学介绍和古籍校勘与研究等多个领域具有重大贡献。他对于五四运动以后的中国社会思想文化发展具有重大影响，蜚声世界文坛，尤其在韩国、日本思想文化领域有极其重要的地位和影响，被誉为“二十世纪东亚文化地图上占最大领土的作家”。</p>
    </div>
  </div>
</div>

<br><br>

<div class="layui-progress" lay-showPercent="true">
  <div class="layui-progress-bar" lay-percent="1/3"></div>
</div>

<br>

<div class="layui-progress">
  <div class="layui-progress-bar layui-bg-red" lay-percent="20%"></div>
</div>
<br>
<div class="layui-progress">
  <div class="layui-progress-bar layui-bg-orange" lay-percent="30%"></div>
</div>
<br>
<div class="layui-progress">
  <div class="layui-progress-bar layui-bg-green" lay-percent="40%"></div>
</div>
<br>
<div class="layui-progress">
  <div class="layui-progress-bar layui-bg-blue" lay-percent="50%"></div>
</div>
<br>
<div class="layui-progress">
  <div class="layui-progress-bar layui-bg-cyan" lay-percent="60%"></div>
</div>
<br>

<div class="layui-progress layui-progress-big" lay-filter="demo" lay-showPercent="true">
  <div class="layui-progress-bar" lay-percent="100%"></div>
</div>
<br>

<div class="layui-progress layui-progress-big" lay-filter="demo" lay-showPercent="true">
  <div class="layui-progress-bar" lay-percent="1700 / 2000"></div>
</div>

<br><br>

<blockquote class="layui-elem-quote">Layui正是你苦苦寻找的前端UI框架</blockquote>
<blockquote class="layui-elem-quote layui-quote-nm">Layui正是你苦苦寻找的前端UI框架Layui正是你苦苦寻找的前端UI框架Layui正是你苦苦寻找的前端UI框架Layui正是你苦苦寻找的前端UI框架Layui正是你苦苦寻找的前端UI框架</blockquote>

<fieldset class="layui-elem-field">
  <legend>字段集区块 - 默认风格</legend>
  <div class="layui-field-box">
    内容区域
  </div>
</fieldset>

默认分割线
<hr>
 
赤色分割线
<hr class="layui-bg-red">
 
橙色分割线
<hr class="layui-bg-orange">
 
墨绿分割线
<hr class="layui-bg-green">
 
青色分割线
<hr class="layui-bg-cyan">
 
蓝色分割线
<hr class="layui-bg-blue">
 
黑色分割线
<hr class="layui-bg-black">
 
灰色分割线
<hr class="layui-bg-gray">

<br><br>

<ul class="layui-nav">
  <li class="layui-nav-item"><a href="">最新活动</a></li>
  <li class="layui-nav-item layui-this">
    <a href="javascript:;">产品</a>
    <dl class="layui-nav-child">
      <dd><a href="javascript:;">选项1</a></dd>
      <dd><a href="javascript:;">选项2</a></dd>
      <dd>
        <a href="javascript:;">选项3</a>
      </dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">大数据<span class="layui-badge-dot"></span></a></li>
  <li class="layui-nav-item">
    <a href="javascript:;">解决方案</a>
    <dl class="layui-nav-child">
      <dd><a href="">移动模块</a></dd>
      <dd class="layui-this"><a href="">后台模版</a></dd>
      <dd><a href="">电商平台</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">社区</a></li>
  <li class="layui-nav-item">
    <a href=""><img src="http://t.cn/RCzsdCq" class="layui-nav-img">我</a>
  </li>
</ul>

<br>

<ul class="layui-nav layui-bg-cyan">
  <li class="layui-nav-item"><a href="">最新活动</a></li>
  <li class="layui-nav-item layui-this">
    <a href="javascript:;">产品</a>
    <dl class="layui-nav-child">
      <dd><a href="">选项1</a></dd>
      <dd><a href="">选项2</a></dd>
      <dd>
        <a href="javascript:;">选项3</a>
      </dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">大数据</a></li>
  <li class="layui-nav-item">
    <a href="javascript:;">解决方案</a>
    <dl class="layui-nav-child">
      <dd><a href="">移动模块</a></dd>
      <dd class="layui-this"><a href="">后台模版</a></dd>
      <dd><a href="">电商平台</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">社区</a></li>
</ul>

<br>

<ul class="layui-nav layui-bg-green">
  <li class="layui-nav-item"><a href="">最新活动</a></li>
  <li class="layui-nav-item layui-this">
    <a href="javascript:;">产品</a>
    <dl class="layui-nav-child">
      <dd><a href="">选项1</a></dd>
      <dd><a href="">选项2</a></dd>
      <dd><a href="">选项3</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">大数据</a></li>
  <li class="layui-nav-item">
    <a href="javascript:;">解决方案</a>
    <dl class="layui-nav-child">
      <dd><a href="">移动模块</a></dd>
      <dd class="layui-this"><a href="">后台模版</a></dd>
      <dd><a href="">电商平台</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">社区</a></li>
</ul>

<br>

<ul class="layui-nav layui-bg-blue">
  <li class="layui-nav-item"><a href="">最新活动</a></li>
  <li class="layui-nav-item layui-this">
    <a href="javascript:;">产品</a>
    <dl class="layui-nav-child">
      <dd><a href="">选项1</a></dd>
      <dd><a href="">选项2</a></dd>
      <dd><a href="">选项3</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">大数据</a></li>
  <li class="layui-nav-item">
    <a href="javascript:;">解决方案</a>
    <dl class="layui-nav-child">
      <dd><a href="">移动模块</a></dd>
      <dd class="layui-this"><a href="">后台模版</a></dd>
      <dd><a href="">电商平台</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">社区</a></li>
</ul>

<br><br>

<ul class="layui-nav layui-nav-tree"  lay-filter="test">
  <li class="layui-nav-item">
    <a class="" href="javascript:;">产品</a>
    <dl class="layui-nav-child">
      <dd><a href="javascript:;">移动模块</a></dd>
      <dd>
        <a href="javascript:;">后台模版</a>
        <dl class="layui-nav-child">
          <dd><a href="javascript:;">组件一</a></dd>
          <dd>
            <a href="javascript:;">组件二</a>
          </dd>
          <dd><a href="javascript:;">组件三</a></dd>
        </dl>
      </dd>
      <dd><a href="javascript:;">电商平台</a></dd>
      <dd><a href="">跳转菜单</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item">
    <a href="javascript:;">解决方案</a>
    <dl class="layui-nav-child">
      <dd><a href="javascript:;">移动模块</a></dd>
      <dd>
        <a href="javascript:;">后台模版</a>
      </dd>
      <dd><a href="">电商平台</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">云市场</a></li>
  <li class="layui-nav-item"><a href="">社区</a></li>
</ul>

<br><br>

<ul class="layui-nav layui-bg-cyan layui-nav-tree"  lay-filter="test">
  <li class="layui-nav-item layui-nav-itemed">
    <a class="" href="javascript:;">产品</a>
    <dl class="layui-nav-child">
      <dd><a href="javascript:;">移动模块</a></dd>
      <dd>
        <a href="javascript:;">后台模版</a>
      </dd>
      <dd><a href="javascript:;">电商平台</a></dd>
      <dd><a href="">跳转菜单</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item">
    <a href="javascript:;">解决方案</a>
    <dl class="layui-nav-child">
      <dd><a href="javascript:;">移动模块</a></dd>
      <dd><a href="javascript:;">后台模版</a></dd>
      <dd><a href="">电商平台</a></dd>
    </dl>
  </li>
  <li class="layui-nav-item"><a href="">云市场</a></li>
  <li class="layui-nav-item"><a href="">社区</a></li>
</ul>


<br><br>

<span class="layui-breadcrumb">
  <a href="">首页</a>
  <a href="">国际新闻</a>
  <a href="">亚太地区</a>
  <a><cite>正文</cite></a>
</span>

<br><br>

<span class="layui-breadcrumb" lay-separator="|">
  <a href="">娱乐</a>
  <a href="">八卦</a>
  <a href="">体育</a>
  <a href="">搞笑</a>
  <a href="">视频</a>
  <a href="">游戏</a>
  <a href="">综艺</a>
</span>

<br><br>

<div class="layui-tab" lay-filter="tabDemo" lay-allowClose="true">
  <ul class="layui-tab-title">
    <li class="layui-this" lay-id="1">标题1</li>
    <li lay-id="2">标题2</li>
    <li lay-id="3">标题3</li>
    <li lay-id="4">标题4</li>
    <li lay-id="5">标题5</li>
  </ul>
  <div class="layui-tab-content">
    <div class="layui-tab-item layui-show">1</div>
    <div class="layui-tab-item">2</div>
    <div class="layui-tab-item">3</div>
    <div class="layui-tab-item">4</div>
    <div class="layui-tab-item">5</div>
  </div>
</div>

<button class="layui-btn" onclick="layui.element.tabChange('tabDemo', 3)">手工切换到“标题3”</button>
<button class="layui-btn" onclick="layui.element.tabAdd('tabDemo', {title:'新标题', content:'新内容', id: +new Date})">添加Tab</button>
<button class="layui-btn" onclick="layui.element.tabDelete('tabDemo', 4)">删除“标题4”</button>

<div class="layui-tab layui-tab-brief">
  <ul class="layui-tab-title">
    <li class="layui-this">标题1</li>
    <li>标题2</li>
    <li>标题3</li>
    <li>标题4</li>
    <li>标题5</li>
    <li>标题6</li>
  </ul>
</div>

<div class="layui-inline">
  <div class="layui-tab layui-tab-card">
    <ul class="layui-tab-title">
      <li class="layui-this">标题一</li>
      <li>标题2</li>
      <li>标题3</li>
      <li>标题4</li>
      <li>标题5</li>
      <li>标题6</li>
    </ul>
    <div class="layui-tab-content">
      <div class="layui-tab-item layui-show">
        <div class="layui-form">
          <select>
            <option>1</option>
            <option>2</option>
            <option>3</option>
            <option>4</option>
            <option>5</option>
            <option>6</option>
          </select>
        </div>
      </div>
      <div class="layui-tab-item">2</div>
      <div class="layui-tab-item">3</div>
      <div class="layui-tab-item">4</div>
      <div class="layui-tab-item">5</div>
      <div class="layui-tab-item">6</div>
    </div>
  </div>
</div>

<div class="layui-tab" lay-filter="test" lay-allowClose="true">
  <ul class="layui-tab-title">
    <li class="layui-this">标题1</li>
    <li>标题2</li>
    <li>标题3</li>
    <li>标题4</li>
    <li>标题5</li>
    <li>标题6</li>
    <li>标题7</li>
    <li>标题8</li>
  </ul>
</div>



<script src="../src/layui.js"></script>
<script>

layui.use(['element', 'form'], function(){
  var element = layui.element;
  
  element.on('tab(test)', function(data){
    console.log(this, data);
  });
  
  element.on('nav(test)', function(elem){
    console.log(elem)
  });
  
  element.on('collapse(test)', function(data){
    console.log(data);
  });
});
</script>
</body>
</html>
