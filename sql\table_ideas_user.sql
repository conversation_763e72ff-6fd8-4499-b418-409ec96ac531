-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-28 22:20:08
-- 服务器版本： 5.7.43-log
-- PHP 版本： 7.3.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_ideas_user`
--

CREATE TABLE `table_ideas_user` (
  `id` int(20) NOT NULL COMMENT '唯一标识',
  `user_name` varchar(20) DEFAULT NULL COMMENT '回复人姓名',
  `user_number` varchar(20) DEFAULT NULL COMMENT '回复人学工号',
  `user_section` varchar(20) DEFAULT NULL COMMENT '回复人所在部门',
  `user_henau_openid` varchar(50) DEFAULT NULL COMMENT '回复人openid',
  `user_approve_type` tinyint(1) DEFAULT NULL COMMENT '每种建议对应一个回复类型，如数字1代表校园卡相关的建议',
  `user_approve_counts` int(10) DEFAULT '0' COMMENT '回复的数量',
  `user_last_approve_time` datetime DEFAULT NULL COMMENT '上次回复时间'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_ideas_user`
--

INSERT INTO `table_ideas_user` (`id`, `user_name`, `user_number`, `user_section`, `user_henau_openid`, `user_approve_type`, `user_approve_counts`, `user_last_approve_time`) VALUES
(1, '王建辉', '2010120024', '信息化办公室', '28bc322815351539fae70ead315ca203', 1, 1, '2023-11-28 21:43:42'),
(2, '王建辉', '2010120024', '信息化办公室', '28bc322815351539fae70ead315ca203', 2, 0, NULL),
(3, '王建辉', '2010120024', '信息化办公室', '28bc322815351539fae70ead315ca203', 3, 0, NULL);

--
-- 转储表的索引
--

--
-- 表的索引 `table_ideas_user`
--
ALTER TABLE `table_ideas_user`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_ideas_user`
--
ALTER TABLE `table_ideas_user`
  MODIFY `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识', AUTO_INCREMENT=4;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
