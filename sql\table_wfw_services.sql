-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2024-01-24 13:56:52
-- 服务器版本： 5.7.43-log
-- PHP 版本： 7.3.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_wfw_services`
--

CREATE TABLE `table_wfw_services` (
  `service_id` bigint(20) NOT NULL,
  `service_module_id` int(2) DEFAULT NULL COMMENT '模块id',
  `service_module_pid` int(2) DEFAULT NULL COMMENT '模块服务pid',
  `service_module_name` varchar(30) DEFAULT NULL COMMENT '模块名称',
  `service_type` varchar(30) DEFAULT NULL COMMENT '模块类型，H5应用或微信小程序',
  `service_name` varchar(30) DEFAULT NULL COMMENT '服务名称',
  `service_section` varchar(30) DEFAULT NULL COMMENT '服务所属部门',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务链接',
  `service_icon` varchar(255) DEFAULT NULL COMMENT '服务图标',
  `service_visit_count` bigint(20) DEFAULT '0' COMMENT '服务访问量',
  `is_henau_lan_service` tinyint(1) DEFAULT NULL COMMENT '是否在校园网下访问',
  `service_visit_user` char(10) DEFAULT '123' COMMENT '用户身份 1本科生 2研究生 3教职工',
  `hot_service` tinyint(1) DEFAULT '0' COMMENT '是否热门服务，1为是',
  `service_wxapp_username` varchar(30) DEFAULT NULL COMMENT '小程序的username',
  `is_new_service` tinyint(1) DEFAULT '0' COMMENT '是否新服务',
  `is_wfw_service` tinyint(1) DEFAULT '0' COMMENT '是否微服务内部服务',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除服务'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_wfw_services`
--

INSERT INTO `table_wfw_services` (`service_id`, `service_module_id`, `service_module_pid`, `service_module_name`, `service_type`, `service_name`, `service_section`, `service_url`, `service_icon`, `service_visit_count`, `is_henau_lan_service`, `service_visit_user`, `hot_service`, `service_wxapp_username`, `is_new_service`, `is_wfw_service`, `is_delete`) VALUES
(1, 1, 1, '综合服务', 'H5APP', '失物招领', NULL, 'https://swzl.henau.edu.cn/swzl/feed/index', '/henauwfw/icon/GeneralService/LosingStuff.svg', 1492, 0, '123', 1, NULL, 0, 0, 0),
(2, 1, 1, '综合服务', 'H5APP', '本科生请假', NULL, 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxf40cdbdcc58c583e&redirect_uri=https%3a%2f%2fstudqj.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect', '/henauwfw/icon/GeneralService/UndergraduateLeave.png', 17920, 0, '13', 1, NULL, 0, 0, 0),
(3, 1, 1, '综合服务', 'H5APP', '研究生请假', NULL, 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6ea65cb285c86754&redirect_uri=https%3a%2f%2fyjs.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE&connect_redirect=1#wechat_redirect', '/henauwfw/icon/GeneralService/GraduateLeave.png', 333, 0, '23', 0, NULL, 0, 0, 0),
(4, 1, 1, '综合服务', 'H5APP', '实验室管理', NULL, 'https://sysxxh.henau.edu.cn/oauthlogin.aspx?type=1', '/henauwfw/icon/GeneralService/Laboratory.png', 1505, 0, '123', 0, NULL, 0, 0, 0),
(39, 1, 1, '综合服务', 'H5APP', '教学管理服务', NULL, 'https://jw.henau.edu.cn', '/henauwfw/icon/GeneralService/TeachSystem.png', 1610, 0, '123', 0, NULL, 0, 0, 0),
(6, 1, 1, '综合服务', 'H5APP', '电子邮箱', NULL, 'https://ac.henau.edu.cn/mail/oauth2/login', '/henauwfw/icon/GeneralService/EmailAddress.png', 104, 0, '3', 0, NULL, 0, 0, 0),
(7, 1, 1, '综合服务', 'H5APP', '电子邮箱', NULL, 'http://mail.stu.henau.edu.cn/', '/henauwfw/icon/GeneralService/EmailAddress.png', 518, 0, '12', 0, NULL, 0, 0, 0),
(8, 1, 1, '综合服务', 'H5APP', '学工管理系统', NULL, 'http://************/xgxt/', '/henauwfw/icon/GeneralService/ManageSystem.png', 239, 1, '123', 0, NULL, 0, 0, 0),
(9, 1, 1, '综合服务', 'H5APP', '访客预约', NULL, 'https://ywtb.henau.edu.cn/default/work/henau/xwryrx/fkdjIndex.jsp', '/henauwfw/icon/GeneralService/Visitor.png', 1255, 0, '123', 1, NULL, 0, 0, 1),
(10, 1, 1, '综合服务', 'H5APP', '访客预约待办', NULL, 'https://ywtb.henau.edu.cn/default/work/shou/meeting/toDo/mobile/managementCenter.jsp', '/henauwfw/icon/GeneralService/Agency.png', 215, 0, '3', 0, NULL, 0, 0, 0),
(11, 2, 2, '资源申请', 'H5APP', '电子邮箱申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/emailApply', '/henauwfw/icon/ResourceApply/EmailApply.png', 1000, 0, '123', 0, NULL, 0, 0, 0),
(12, 2, 2, '资源申请', 'H5APP', 'VPN账号申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/vpnApply', '/henauwfw/icon/ResourceApply/VPNApply.png', 963, 0, '123', 0, NULL, 0, 0, 0),
(14, 2, 2, '资源申请', 'H5APP', '公网IP申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/ipApply', '/henauwfw/icon/ResourceApply/IPApply.png', 20, 0, '3', 0, NULL, 0, 0, 0),
(15, 2, 2, '资源申请', 'H5APP', '服务器申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/serverApply', '/henauwfw/icon/ResourceApply/ServerApply.png', 24, 0, '3', 0, NULL, 0, 0, 0),
(13, 2, 2, '资源申请', 'H5APP', '多媒体教室', '信息化办公室', 'https://itcservices.henau.edu.cn/#/multiMediaApply', '/henauwfw/icon/ResourceApply/ClassroomApply.png', 247, 0, '123', 0, NULL, 0, 0, 0),
(16, 2, 2, '资源申请', 'H5APP', '域名申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/domainApply', '/henauwfw/icon/ResourceApply/DNSApply.png', 17, 0, '3', 0, NULL, 0, 0, 0),
(17, 2, 2, '资源申请', 'H5APP', '网络故障报修', '信息化办公室', 'https://itcservices.henau.edu.cn/#/networkRepairApply', '/henauwfw/icon/ResourceApply/InternetWrong.png', 147, 0, '123', 0, NULL, 0, 0, 0),
(18, 2, 2, '资源申请', 'H5APP', '校园网注册', '信息化办公室', 'https://oauth.henau.edu.cn/app/CNPRS', '/henauwfw/icon/ResourceApply/CampusNetwork.png', 1210, 0, '123', 1, NULL, 0, 0, 0),
(19, 3, 3, '学习服务', 'H5APP', '四六级查询', NULL, 'https://cjcx.neea.edu.cn/xhtml1/folder/21045/4883-1.htm', '/henauwfw/icon/LearningService/GradeInquiry.png', 742, 0, '123', 0, NULL, 0, 0, 0),
(20, 3, 3, '学习服务', 'H5APP', '青年大学习', NULL, 'http://hnqndaxuexi.dahejs.cn/study/index', '/henauwfw/icon/LearningService/YoungthLearning.png', 1446, 0, '123', 0, NULL, 0, 0, 0),
(21, 3, 3, '学习服务', 'H5APP', '农大校历', NULL, 'https://jw.henau.edu.cn/public/SchoolCalendar.jsp', '/henauwfw/icon/LearningService/CalendarInquiry.png', 1792, 0, '123', 0, NULL, 0, 0, 0),
(22, 3, 3, '学习服务', 'H5APP', '代码托管', NULL, 'https://git.henau.edu.cn', '/henauwfw/icon/LearningService/GitCode.png', 325, 0, '123', 0, NULL, 0, 0, 0),
(23, 3, 3, '学习服务', 'H5APP', '图书馆预约', NULL, 'http://hnnd.zhixinst.com/mid.aspx?url=http://www.skalibrary.com/check?school=henau&&flag=0&&leixing=0', '/henauwfw/icon/LearningService/LibraryReservation.png', 620, 1, '123', 0, NULL, 0, 0, 0),
(24, 3, 3, '学习服务', 'H5APP', 'HENAUOJ', NULL, 'http://172.23.79.51', '/henauwfw/icon/LearningService/HENAUOJ.png', 122, 1, '123', 0, NULL, 0, 0, 0),
(25, 3, 3, '学习服务', 'WXAPP', '课表查询', NULL, 'pages/schoolStudy/schedule/index.html?eventType=click', '/henauwfw/icon/LearningService/ScheduleInquiry.png', 1440, 0, '123', 0, 'gh_912e4d606ec4', 0, 0, 0),
(26, 3, 3, '学习服务', 'WXAPP', '成绩查询', NULL, 'pages/schoolStudy/grade/grade.html?eventType=click', '/henauwfw/icon/LearningService/ExamInquiry.png', 10008, 0, '123', 0, 'gh_912e4d606ec4', 0, 0, 0),
(27, 3, 3, '学习服务', 'WXAPP', '考试查询', NULL, 'pages/schoolStudy/exam/exam.html?eventType=click', '/henauwfw/icon/LearningService/Exam.png', 5770, 0, '123', 0, 'gh_912e4d606ec4', 0, 0, 0),
(28, 3, 3, '学习服务', 'WXAPP', '学籍注册', NULL, 'pages/web/web.html?path=https://xwx.gzzmedu.com:6899/html/face-recognition.html&eventType=click', '/henauwfw/icon/LearningService/StudentRegistration.png', 236, 0, '123', 0, 'gh_912e4d606ec4', 0, 0, 0),
(29, 3, 3, '学习服务', 'WXAPP', '教资查询', NULL, 'packageResultQuery/pages/ntce_his/NTCE_Result_His_Self.html', '/henauwfw/icon/LearningService/TeachingInquiry.png', 345, 0, '123', 0, 'gh_9bc87509b26b', 0, 0, 0),
(30, 3, 3, '学习服务', 'WXAPP', '电子成绩单', NULL, 'pages/workService/electronicReportCard/index.html?eventType=click', '/henauwfw/icon/LearningService/Transcript.png', 1606, 0, '123', 0, 'gh_912e4d606ec4', 0, 0, 0),
(31, 4, 4, '我的信息', 'H5APP', '更新账户信息', '信息化办公室', 'https://oauth.henau.edu.cn/account/syncinfo', '/henauwfw/icon/MyMessage/ChangeInformation.png', 637, 0, '123', 0, NULL, 0, 0, 0),
(32, 4, 4, '我的信息', 'H5APP', '修改手机号码', '信息化办公室', 'https://oauth.henau.edu.cn/account/bindsms?proactive_change=1', '/henauwfw/icon/MyMessage/ModifyPhoneNumber.png', 146, 0, '123', 0, NULL, 0, 0, 0),
(33, 5, 5, '建议反馈', 'H5APP', '信息立交桥', NULL, 'https://www.henau.edu.cn/xzxx_tj.jsp?urltype=leadermail.LeaderMailsAddUrl&wbtreeid=1164', '/henauwfw/icon/SuggestionFeedback/InformationConnect.png', 636, 0, '123', 0, NULL, 0, 0, 0),
(34, 5, 5, '建议反馈', 'H5APP', '灵感小站', '信息化办公室', 'SubmitFeedback', '/henauwfw/icon/SuggestionFeedback/InspirationStation.png', 602, 0, '123', 1, NULL, 0, 1, 0),
(35, 1, 1, '综合服务', 'H5APP', '人事管理系统', NULL, 'https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd5a8ef170d1172180&redirect_uri=http%3A%2F%2F172.31.0.135%3A8080%2FwebhrN2SSO&response_type=code&scope=henauapi_login&state=STATE', '/henauwfw/icon/GeneralService/add_two.svg', 43, 1, '3', 0, NULL, 0, 0, 0),
(36, 1, 1, '综合服务', 'H5APP', '仪器共享平台', NULL, 'https://yqgx.henau.edu.cn/', '/henauwfw/icon/GeneralService/InstrumentShare.png', 412, 1, '123', 0, NULL, 0, 0, 0),
(37, 1, 1, '综合服务', 'H5APP', '校园服务电话', '信息化办公室', 'Telephone', '/henauwfw/icon/GeneralService/Telephone.png', 731, 0, '123', 1, NULL, 0, 1, 0),
(38, 4, 4, '我的信息', 'H5APP', '我的消息', '信息化办公室', 'MyMessage', '/henauwfw/icon/GeneralService/MyMessage.png', 2078, 0, '123', 0, NULL, 0, 1, 0),
(5, 1, 1, '综合服务', 'H5APP', 'Chat AI', '信息化办公室', 'https://ac.henau.edu.cn/aigc/public/chat/', '/henauwfw/icon/GeneralService/HenauChat.png', 5974, 0, '123', 1, NULL, 1, 0, 0),
(40, 4, 4, '我的信息', 'H5APP', '自主采集人脸', NULL, 'https://bwcfr.henau.edu.cn/weixin/?type=1', '/henauwfw/icon/MyMessage/AutoCollectFace.png', 376, 0, '12', 0, NULL, 0, 0, 0),
(41, 4, 4, '我的信息', 'H5APP', '自主采集人脸', NULL, 'https://bwcfr.henau.edu.cn/weixin/?type=2', '/henauwfw/icon/MyMessage/AutoCollectFace.png', 102, 0, '3', 0, NULL, 0, 0, 0),
(42, 1, 1, '综合服务', 'H5APP', '学生自主选宿', NULL, 'https://bwcfr.henau.edu.cn/weixin/?type=4', '/henauwfw/icon/GeneralService/SelectDormitory.png', 1280, 0, '12', 0, NULL, 0, 0, 0),
(43, 1, 1, '综合服务', 'H5APP', '校内业务用表', '信息化办公室', 'BusinessForm', '/henauwfw/icon/GeneralService/BusinessForm.png', 1, 0, '123', 0, NULL, 0, 1, 1),
(44, 1, 1, '综合服务', 'H5APP', '园区服务预约', NULL, 'https://yqfw.henau.edu.cn/', '/henauwfw/icon/GeneralService/ParkServiceReservation.png', 1, 0, '123', 0, NULL, 1, 0, 0);

--
-- 转储表的索引
--

--
-- 表的索引 `table_wfw_services`
--
ALTER TABLE `table_wfw_services`
  ADD PRIMARY KEY (`service_id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_wfw_services`
--
ALTER TABLE `table_wfw_services`
  MODIFY `service_id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=400;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
