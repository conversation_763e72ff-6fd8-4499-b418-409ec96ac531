-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-28 22:13:08
-- 服务器版本： 5.7.43-log
-- PHP 版本： 7.3.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_ideas_template_message_log`
--

CREATE TABLE `table_ideas_template_message_log` (
  `template_message_id` bigint(20) NOT NULL,
  `template_message_send_time` datetime DEFAULT NULL COMMENT '模板消息发送时间',
  `template_message_sender_ip` varchar(200) DEFAULT NULL,
  `template_message_sender_ua` varchar(700) DEFAULT NULL,
  `template_message_sender_name` varchar(20) DEFAULT NULL COMMENT '模板消息发送者学工号',
  `template_message_sender_number` varchar(20) DEFAULT NULL,
  `template_message_sender_section` varchar(20) DEFAULT NULL,
  `template_message_sender_henau_openid` varchar(50) DEFAULT NULL,
  `template_message_receiver_name` varchar(20) DEFAULT NULL,
  `template_message_receiver_number` varchar(20) DEFAULT NULL COMMENT '模板消息接收者学工号',
  `template_message_receiver_section` varchar(20) DEFAULT NULL,
  `template_message_receiver_henau_openid` varchar(50) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_ideas_user`
--

CREATE TABLE `table_ideas_user` (
  `id` int(20) NOT NULL COMMENT '唯一标识',
  `user_name` varchar(20) DEFAULT NULL COMMENT '回复人姓名',
  `user_number` varchar(20) DEFAULT NULL COMMENT '回复人学工号',
  `user_section` varchar(20) DEFAULT NULL COMMENT '回复人所在部门',
  `user_henau_openid` varchar(50) DEFAULT NULL COMMENT '回复人openid',
  `user_approve_type` tinyint(1) DEFAULT NULL COMMENT '每种建议对应一个回复类型，如数字1代表校园卡相关的建议',
  `user_approve_counts` int(10) DEFAULT '0' COMMENT '回复的数量',
  `user_last_approve_time` datetime DEFAULT NULL COMMENT '上次回复时间'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_ideas_user`
--

INSERT INTO `table_ideas_user` (`id`, `user_name`, `user_number`, `user_section`, `user_henau_openid`, `user_approve_type`, `user_approve_counts`, `user_last_approve_time`) VALUES
(1, '王建辉', '2010120024', '信息化办公室', '28bc322815351539fae70ead315ca203', 1, 1, '2023-11-28 21:43:42'),
(2, '王建辉', '2010120024', '信息化办公室', '28bc322815351539fae70ead315ca203', 2, 0, NULL),
(3, '王建辉', '2010120024', '信息化办公室', '28bc322815351539fae70ead315ca203', 3, 0, NULL);

-- --------------------------------------------------------

--
-- 表的结构 `table_ideas_visit`
--

CREATE TABLE `table_ideas_visit` (
  `visit_id` bigint(50) NOT NULL,
  `visit_time` datetime DEFAULT NULL,
  `visit_ua` varchar(700) DEFAULT NULL,
  `visit_ip` varchar(100) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_info`
--

CREATE TABLE `table_info` (
  `user_id` bigint(20) NOT NULL,
  `user_number` varchar(20) NOT NULL,
  `user_name` varchar(10) DEFAULT NULL,
  `user_section` varchar(50) DEFAULT NULL,
  `user_henau_openid` varchar(255) DEFAULT NULL,
  `user_ideas_type` int(2) DEFAULT NULL,
  `user_phone` varchar(15) NOT NULL,
  `user_ideas_attachment` varchar(255) DEFAULT NULL,
  `approve_user_name` varchar(20) DEFAULT NULL,
  `approve_user_number` varchar(20) DEFAULT NULL,
  `approve_user_section` varchar(20) DEFAULT NULL,
  `approve_user_henau_openid` varchar(50) DEFAULT NULL,
  `approve_user_reply_content` text,
  `user_ideas` text NOT NULL,
  `user_submit_time` datetime DEFAULT NULL,
  `has_approved` tinyint(1) NOT NULL DEFAULT '0',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `user_write_time` varchar(30) DEFAULT NULL,
  `user_ip` varchar(100) DEFAULT NULL,
  `user_ua` varchar(700) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_it_studio_sign`
--

CREATE TABLE `table_it_studio_sign` (
  `sign_id` bigint(20) NOT NULL,
  `sign_time` datetime DEFAULT NULL,
  `campus` varchar(10) DEFAULT NULL,
  `college` varchar(30) DEFAULT NULL,
  `major` varchar(50) DEFAULT NULL,
  `name` varchar(10) DEFAULT NULL,
  `stu_number` char(10) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `qq_number` varchar(20) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `identity` varchar(20) DEFAULT NULL,
  `first_dirction` varchar(20) DEFAULT NULL,
  `second_dirction` varchar(20) DEFAULT NULL,
  `speciality` varchar(700) DEFAULT NULL,
  `experience` varchar(700) DEFAULT NULL,
  `sign_ua` varchar(1000) DEFAULT NULL,
  `sign_ip` varchar(100) NOT NULL,
  `photo_save_name` varchar(255) DEFAULT NULL,
  `henau_openid` varchar(64) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_it_user_visit`
--

CREATE TABLE `table_it_user_visit` (
  `visit_id` bigint(20) NOT NULL,
  `visit_user_name` varchar(10) DEFAULT NULL,
  `visit_user_number` varchar(15) DEFAULT NULL,
  `visit_user_section` varchar(255) DEFAULT NULL,
  `visit_user_henau_openid` varchar(64) DEFAULT NULL,
  `visit_user_phone` varchar(15) DEFAULT NULL,
  `visit_ip` varchar(100) DEFAULT NULL,
  `visit_time` datetime DEFAULT NULL,
  `visit_ua` varchar(1000) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_message_visit`
--

CREATE TABLE `table_message_visit` (
  `visit_id` bigint(50) NOT NULL,
  `visit_ip` varchar(70) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visit_time` datetime DEFAULT NULL,
  `visit_ua` varchar(700) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_name` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_number` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_section` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_henau_openid` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_msg_log`
--

CREATE TABLE `table_msg_log` (
  `log_id` int(11) NOT NULL,
  `template_number` varchar(255) DEFAULT NULL,
  `message_title` varchar(255) DEFAULT NULL,
  `message_content` char(255) DEFAULT NULL,
  `message_url` char(255) DEFAULT NULL,
  `wx_msgid` varchar(255) DEFAULT NULL,
  `send_time` datetime DEFAULT NULL,
  `send_pid` varchar(255) DEFAULT NULL,
  `send_uid` int(11) DEFAULT NULL,
  `send_user_number` varchar(60) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT NULL,
  `read_time` datetime DEFAULT NULL,
  `url_visit_count` int(11) DEFAULT NULL,
  `soft_delete` tinyint(1) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_msg_log`
--

INSERT INTO `table_msg_log` (`log_id`, `template_number`, `message_title`, `message_content`, `message_url`, `wx_msgid`, `send_time`, `send_pid`, `send_uid`, `send_user_number`, `is_read`, `read_time`, `url_visit_count`, `soft_delete`) VALUES
(359, '323caa799ef44d3b077e5b69f62dc633', '河南农业大学电子邮箱申请', '您的电子邮箱申请已被驳回', 'https://itcservices.henau.edu.cn/#/myApplies', '2958516060761899014', '2023-06-05 10:52:49', '100008', 100005210, '2010120024', 1, NULL, NULL, 0),
(362, '323caa799ef44d3b077e5b69f62dc633', '河南农业大学电子邮箱申请', '您的电子邮箱申请已通过审核', 'https://itcservices.henau.edu.cn/#/myApplies', '2958870458126057474', '2023-06-05 16:44:53', '100008', 100005210, '2010120024', 1, NULL, NULL, 0),
(363, '323caa799ef44d3b077e5b69f62dc633', '河南农业大学网络故障报修申请', '来自信管学院 王建辉（电话：19999999999）的申请', 'https://itcservices.henau.edu.cn/#/myApplies', '2958899239944716290', '2023-06-05 17:13:28', '100008', 100000015, '2104183160', 1, NULL, NULL, 0);

-- --------------------------------------------------------

--
-- 表的结构 `table_notice_create`
--

CREATE TABLE `table_notice_create` (
  `notice_id` int(10) NOT NULL COMMENT '公告ID',
  `notice_creat_time` datetime DEFAULT NULL COMMENT '公告创建时间',
  `notice_creat_user_number` char(15) DEFAULT NULL COMMENT '公告发布者学工号',
  `notice_title` varchar(50) NOT NULL COMMENT '公告标题',
  `notice_name` varchar(50) NOT NULL,
  `notice_text` text COMMENT '公告内容',
  `notice_views` int(50) NOT NULL COMMENT '公告查看人数',
  `notice_creat_user_ip` varchar(100) DEFAULT NULL COMMENT '公告发布者IP',
  `notice_creat_user_ua` varchar(700) DEFAULT NULL COMMENT '公告发布者UA',
  `soft_delete` tinyint(1) DEFAULT NULL COMMENT '软删除'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_notice_create`
--

INSERT INTO `table_notice_create` (`notice_id`, `notice_creat_time`, `notice_creat_user_number`, `notice_title`, `notice_name`, `notice_text`, `notice_views`, `notice_creat_user_ip`, `notice_creat_user_ua`, `soft_delete`) VALUES
(1, '2022-05-30 12:19:25', '', 'NoticeWfw', '微服务平台上线了', NULL, 335, NULL, NULL, 0),
(2, '2022-10-09 12:19:25', NULL, 'NoticeIdea', '“灵感小站”上线啦，快投递你的好想法吧！', NULL, 337, NULL, NULL, 0),
(3, '2022-12-12 12:20:04', '', 'NoticeDark', '追寻你的偏好：深色模式适配', NULL, 643, NULL, NULL, 0),
(4, '2023-03-02 02:19:19', NULL, 'NoticeWeappClassify', '引入微信小程序并对服务进行分类', NULL, 581, NULL, NULL, 0),
(5, '2023-09-01 14:30:00', NULL, 'NoticeUpdatePlatform', '不了解微服务？快来看看吧', NULL, 381, NULL, NULL, 0),
(6, '2023-09-01 14:30:00', NULL, 'NoticeWfwPlus', '微服务升级啦，全新平台等你来体验！', NULL, 383, NULL, NULL, 0);

-- --------------------------------------------------------

--
-- 表的结构 `table_notice_view`
--

CREATE TABLE `table_notice_view` (
  `notice_id` int(11) NOT NULL COMMENT '公告ID',
  `notice_view_user_ip` varchar(100) NOT NULL COMMENT '公告访问者IP',
  `notice_view_user_ua` varchar(700) NOT NULL COMMENT '公告访问者UA',
  `notice_view_time` datetime NOT NULL COMMENT '公告访问时间',
  `notice_view_user_name` varchar(20) DEFAULT NULL,
  `notice_view_user_number` varchar(20) DEFAULT NULL,
  `notice_view_user_section` varchar(20) DEFAULT NULL,
  `notice_view_user_henau_openid` varchar(50) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_request_rate_limit`
--

CREATE TABLE `table_request_rate_limit` (
  `time_stamp` varchar(50) NOT NULL,
  `request_time` varchar(50) DEFAULT NULL,
  `real_ip` varchar(50) NOT NULL,
  `request_num` int(3) NOT NULL,
  `request_api` varchar(50) NOT NULL,
  `limit_time` varchar(20) DEFAULT NULL,
  `request_api_origin` varchar(30) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_request_rate_limit_bak`
--

CREATE TABLE `table_request_rate_limit_bak` (
  `time_stamp` varchar(50) NOT NULL,
  `request_time` varchar(50) DEFAULT NULL,
  `real_ip` varchar(50) NOT NULL,
  `request_num` int(3) NOT NULL,
  `request_api` varchar(50) NOT NULL,
  `limit_time` varchar(20) DEFAULT NULL,
  `request_api_origin` varchar(30) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_services_visit`
--

CREATE TABLE `table_services_visit` (
  `visit_id` bigint(20) NOT NULL,
  `visit_time` datetime DEFAULT NULL,
  `visit_ua` varchar(700) DEFAULT NULL,
  `visit_ip` varchar(200) DEFAULT NULL,
  `visit_service_id` int(11) DEFAULT NULL COMMENT '服务唯一标识符',
  `visit_service_name` varchar(20) DEFAULT NULL COMMENT '服务名称',
  `visitor_name` varchar(20) DEFAULT NULL,
  `visitor_number` varchar(20) DEFAULT NULL,
  `visitor_section` varchar(20) DEFAULT NULL,
  `visitor_henau_openid` varchar(50) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_stu_information`
--

CREATE TABLE `table_stu_information` (
  `info_id` int(2) NOT NULL,
  `info_type` int(2) NOT NULL,
  `info_content` varchar(50) NOT NULL,
  `is_delete` tinyint(1) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- --------------------------------------------------------

--
-- 表的结构 `table_visit`
--

CREATE TABLE `table_visit` (
  `visit_id` bigint(50) NOT NULL,
  `visit_ip` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visit_time` datetime DEFAULT NULL,
  `visit_ua` text COLLATE utf8_unicode_ci,
  `visiter_user_name` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_number` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_section` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `visiter_user_henau_openid` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `table_wfw_services`
--

CREATE TABLE `table_wfw_services` (
  `service_id` bigint(20) NOT NULL,
  `service_module_id` int(2) DEFAULT NULL COMMENT '模块id',
  `service_module_pid` int(2) DEFAULT NULL COMMENT '模块服务pid',
  `service_module_name` varchar(30) DEFAULT NULL COMMENT '模块名称',
  `service_type` varchar(30) DEFAULT NULL COMMENT '模块类型，H5应用或微信小程序',
  `service_name` varchar(30) DEFAULT NULL COMMENT '服务名称',
  `service_section` varchar(30) DEFAULT NULL COMMENT '服务所属部门',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务链接',
  `service_icon` varchar(255) DEFAULT NULL COMMENT '服务图标',
  `service_visit_count` bigint(20) DEFAULT '0' COMMENT '服务访问量',
  `service_visit_user` tinyint(1) DEFAULT NULL COMMENT '服务为谁开放，0表示仅为本科生，1表示研究生及以上，2表示教职工',
  `is_henau_lan_service` tinyint(1) DEFAULT NULL COMMENT '是否在校园网下访问',
  `hot_service` tinyint(1) DEFAULT '0' COMMENT '是否热门服务，1为是',
  `service_wxapp_username` varchar(30) DEFAULT NULL COMMENT '小程序的username',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除服务'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_wfw_services`
--

INSERT INTO `table_wfw_services` (`service_id`, `service_module_id`, `service_module_pid`, `service_module_name`, `service_type`, `service_name`, `service_section`, `service_url`, `service_icon`, `service_visit_count`, `service_visit_user`, `is_henau_lan_service`, `hot_service`, `service_wxapp_username`, `is_delete`) VALUES
(1, 1, 1, '综合服务', 'H5APP', '失物招领', NULL, 'https://swzl.henau.edu.cn/swzl/feed/index', '/henauwfw/icon/GeneralService/LosingStuff.svg', 0, 0, 0, 1, NULL, 0),
(2, 1, 1, '综合服务', 'H5APP', '本科生请假', NULL, 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxf40cdbdcc58c583e&redirect_uri=https%3a%2f%2fstudqj.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect', '/henauwfw/icon/GeneralService/UndergraduateLeave.png', 1, 0, 0, 1, NULL, 0),
(3, 1, 1, '综合服务', 'H5APP', '研究生请假', NULL, 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6ea65cb285c86754&redirect_uri=https%3a%2f%2fyjs.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE&connect_redirect=1#wechat_redirect', '/henauwfw/icon/GeneralService/GraduateLeave.png', 0, 0, 0, 1, NULL, 0),
(4, 1, 1, '综合服务', 'H5APP', '实验室管理', NULL, 'https://sysxxh.henau.edu.cn/oauthlogin.aspx?type=1', '/henauwfw/icon/GeneralService/Laboratory.png', 0, 0, 0, 0, NULL, 0),
(5, 1, 1, '综合服务', 'H5APP', '教学管理服务', NULL, 'https://jw.henau.edu.cn', '/henauwfw/icon/GeneralService/TeachSystem.png', 0, 0, 0, 0, NULL, 0),
(6, 1, 1, '综合服务', 'H5APP', '电子邮箱', NULL, 'https://ac.henau.edu.cn/mail/oauth2/login', '/henauwfw/icon/GeneralService/EmailAddress.png', 0, 2, 0, 0, NULL, 0),
(7, 1, 1, '综合服务', 'H5APP', '电子邮箱', NULL, 'http://mail.stu.henau.edu.cn/', '/henauwfw/icon/GeneralService/EmailAddress.png', 0, 0, 0, 0, NULL, 0),
(8, 1, 1, '综合服务', 'H5APP', '学工管理系统', NULL, 'http://************/xgxt/', '/henauwfw/icon/GeneralService/ManageSystem.png', 0, 0, 1, 0, NULL, 0),
(9, 1, 1, '综合服务', 'H5APP', '访客预约', NULL, 'https://ywtb.henau.edu.cn/default/work/henau/xwryrx/fkdjIndex.jsp', '/henauwfw/icon/GeneralService/Visitor.png', 0, 0, 0, 1, NULL, 0),
(10, 1, 1, '综合服务', 'H5APP', '访客预约待办', NULL, 'https://ywtb.henau.edu.cn/default/work/shou/meeting/toDo/mobile/managementCenter.jsp', '/henauwfw/icon/GeneralService/Agency.png', 0, 2, 0, 0, NULL, 0),
(11, 2, 2, '资源申请', 'H5APP', '电子邮箱申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/emailApply', '/henauwfw/icon/ResourceApply/EmailApply.png', 0, 0, 0, 0, NULL, 0),
(12, 2, 2, '资源申请', 'H5APP', 'VPN账号申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/vpnApply', '/henauwfw/icon/ResourceApply/VPNApply.png', 0, 1, 0, 0, NULL, 0),
(14, 2, 2, '资源申请', 'H5APP', '公网IP申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/ipApply', '/henauwfw/icon/ResourceApply/IPApply.png', 0, 2, 0, 0, NULL, 0),
(15, 2, 2, '资源申请', 'H5APP', '服务器申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/serverApply', '/henauwfw/icon/ResourceApply/ServerApply.png', 0, 2, 0, 0, NULL, 0),
(13, 2, 2, '资源申请', 'H5APP', '多媒体教室', '信息化办公室', 'https://itcservices.henau.edu.cn/#/multiMediaApply', '/henauwfw/icon/ResourceApply/ClassroomApply.png', 0, 0, 0, 0, NULL, 0),
(16, 2, 2, '资源申请', 'H5APP', '域名申请', '信息化办公室', 'https://itcservices.henau.edu.cn/#/domainApply', '/henauwfw/icon/ResourceApply/DNSApply.png', 0, 2, 0, 0, NULL, 0),
(17, 2, 2, '资源申请', 'H5APP', '网络故障报修', '信息化办公室', 'https://itcservices.henau.edu.cn/#/networkRepairApply', '/henauwfw/icon/ResourceApply/InternetWrong.png', 0, 0, 0, 0, NULL, 0),
(18, 2, 2, '资源申请', 'H5APP', '校园网注册', '信息化办公室', 'https://oauth.henau.edu.cn/app/CNPRS', '/henauwfw/icon/ResourceApply/CampusNetwork.png', 0, 0, 0, 1, NULL, 0),
(19, 3, 3, '学习服务', 'H5APP', '四六级查询', NULL, 'https://cet.neea.edu.cn/cet/query.html', '/henauwfw/icon/LearningService/GradeInquiry.png', 0, 0, 0, 0, NULL, 0),
(20, 3, 3, '学习服务', 'H5APP', '青年大学习', NULL, 'http://hnqndaxuexi.dahejs.cn/study/index', '/henauwfw/icon/LearningService/YoungthLearning.png', 0, 0, 0, 0, NULL, 0),
(21, 3, 3, '学习服务', 'H5APP', '农大校历', NULL, 'https://jw.henau.edu.cn/public/SchoolCalendar.jsp', '/henauwfw/icon/LearningService/CalendarInquiry.png', 0, 0, 0, 0, NULL, 0),
(22, 3, 3, '学习服务', 'H5APP', '代码托管', NULL, 'https://git.henau.edu.cn', '/henauwfw/icon/LearningService/GitCode.png', 0, 0, 0, 0, NULL, 0),
(23, 3, 3, '学习服务', 'H5APP', '图书馆预约', NULL, 'http://hnnd.zhixinst.com/mid.aspx?url=http://www.skalibrary.com/check?school=henau&&flag=0&&leixing=0', '/henauwfw/icon/LearningService/LibraryReservation.png', 0, 0, 1, 0, NULL, 0),
(24, 3, 3, '学习服务', 'H5APP', 'HENAUOJ', NULL, 'http://172.23.79.51', '/henauwfw/icon/LearningService/HENAUOJ.png', 0, 0, 1, 0, NULL, 0),
(25, 3, 3, '学习服务', 'WXAPP', '课表查询', NULL, 'pages/schoolStudy/schedule/index.html?eventType=click', '/henauwfw/icon/LearningService/ScheduleInquiry.png', 0, 0, 0, 0, 'gh_912e4d606ec4', 0),
(26, 3, 3, '学习服务', 'WXAPP', '成绩查询', NULL, 'pages/schoolStudy/grade/grade.html?eventType=click', '/henauwfw/icon/LearningService/ExamInquiry.png', 0, 0, 0, 0, 'gh_912e4d606ec4', 0),
(27, 3, 3, '学习服务', 'WXAPP', '考试查询', NULL, 'pages/schoolStudy/exam/exam.html?eventType=click', '/henauwfw/icon/LearningService/Exam.png', 0, 0, 0, 0, 'gh_912e4d606ec4', 0),
(28, 3, 3, '学习服务', 'WXAPP', '学籍注册', NULL, 'pages/web/web.html?path=https://xwx.gzzmedu.com:6899/html/face-recognition.html&eventType=click', '/henauwfw/icon/LearningService/StudentRegistration.png', 0, 0, 0, 0, 'gh_912e4d606ec4', 0),
(29, 3, 3, '学习服务', 'WXAPP', '教资查询', NULL, 'packageResultQuery/pages/ntce_his/NTCE_Result_His_Self.html', '/henauwfw/icon/LearningService/TeachingInquiry.png', 0, 0, 0, 0, 'gh_9bc87509b26b', 0),
(30, 3, 3, '学习服务', 'WXAPP', '电子成绩单', NULL, 'pages/workService/electronicReportCard/index.html?eventType=click', '/henauwfw/icon/LearningService/Transcript.png', 0, 0, 0, 0, 'gh_912e4d606ec4', 0),
(31, 4, 4, '我的信息', 'H5APP', '更新账户信息', '信息化办公室', 'https://oauth.henau.edu.cn/account/syncinfo', '/henauwfw/icon/MyMessage/ChangeInformation.png', 0, 0, 0, 0, NULL, 0),
(32, 4, 4, '我的信息', 'H5APP', '修改手机号码', '信息化办公室', 'https://oauth.henau.edu.cn/account/bindsms?proactive_change=1', '/henauwfw/icon/MyMessage/ModifyPhoneNumber.png', 1, 0, 0, 0, NULL, 0),
(33, 5, 5, '建议反馈', 'H5APP', '信息立交桥', NULL, 'https://www.henau.edu.cn/xzxx_tj.jsp?urltype=leadermail.LeaderMailsAddUrl&wbtreeid=1164', '/henauwfw/icon/SuggestionFeedback/InformationConnect.png', 1, 0, 0, 0, '', 0),
(34, 5, 5, '建议反馈', 'H5APP', '灵感小站', '信息化办公室', 'SubmitFeedback', '/henauwfw/icon/SuggestionFeedback/InspirationStation.png', 15, 0, 0, 1, NULL, 0),
(35, 1, 1, '综合服务', 'H5APP', '人事管理系统', NULL, 'https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd5a8ef170d1172180&redirect_uri=http%3A%2F%2F172.31.0.135%3A8080%2FwebhrN2SSO&response_type=code&scope=henauapi_login&state=STATE', '/henauwfw/icon/GeneralService/add_two.svg', 0, 2, 1, 0, NULL, 0),
(36, 1, 1, '综合服务', 'H5APP', '仪器共享平台', NULL, 'https://yqgx.henau.edu.cn/', '/henauwfw/icon/GeneralService/InstrumentShare.png', 0, 0, 1, 0, NULL, 0),
(37, 1, 1, '综合服务', 'H5APP', '校园服务电话', '信息化办公室', 'Telephone', '/henauwfw/icon/GeneralService/Telephone.png', 1, 0, 0, 1, NULL, 0),
(38, 1, 1, '综合服务', 'H5APP', '我的消息', '信息化办公室', 'MyMessage', '/henauwfw/icon/GeneralService/MyMessage.png', 9, 0, 0, 1, NULL, 0);

--
-- 转储表的索引
--

--
-- 表的索引 `table_ideas_template_message_log`
--
ALTER TABLE `table_ideas_template_message_log`
  ADD PRIMARY KEY (`template_message_id`) USING BTREE;

--
-- 表的索引 `table_ideas_user`
--
ALTER TABLE `table_ideas_user`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `table_ideas_visit`
--
ALTER TABLE `table_ideas_visit`
  ADD PRIMARY KEY (`visit_id`) USING BTREE;

--
-- 表的索引 `table_info`
--
ALTER TABLE `table_info`
  ADD PRIMARY KEY (`user_id`) USING BTREE;

--
-- 表的索引 `table_it_studio_sign`
--
ALTER TABLE `table_it_studio_sign`
  ADD PRIMARY KEY (`sign_id`) USING BTREE;

--
-- 表的索引 `table_it_user_visit`
--
ALTER TABLE `table_it_user_visit`
  ADD PRIMARY KEY (`visit_id`) USING BTREE;

--
-- 表的索引 `table_message_visit`
--
ALTER TABLE `table_message_visit`
  ADD PRIMARY KEY (`visit_id`) USING BTREE;

--
-- 表的索引 `table_msg_log`
--
ALTER TABLE `table_msg_log`
  ADD PRIMARY KEY (`log_id`) USING BTREE;

--
-- 表的索引 `table_notice_create`
--
ALTER TABLE `table_notice_create`
  ADD PRIMARY KEY (`notice_id`) USING BTREE;

--
-- 表的索引 `table_services_visit`
--
ALTER TABLE `table_services_visit`
  ADD PRIMARY KEY (`visit_id`) USING BTREE;

--
-- 表的索引 `table_stu_information`
--
ALTER TABLE `table_stu_information`
  ADD PRIMARY KEY (`info_id`) USING BTREE;

--
-- 表的索引 `table_visit`
--
ALTER TABLE `table_visit`
  ADD PRIMARY KEY (`visit_id`);

--
-- 表的索引 `table_wfw_services`
--
ALTER TABLE `table_wfw_services`
  ADD PRIMARY KEY (`service_id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_ideas_template_message_log`
--
ALTER TABLE `table_ideas_template_message_log`
  MODIFY `template_message_id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_ideas_user`
--
ALTER TABLE `table_ideas_user`
  MODIFY `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识', AUTO_INCREMENT=4;

--
-- 使用表AUTO_INCREMENT `table_ideas_visit`
--
ALTER TABLE `table_ideas_visit`
  MODIFY `visit_id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_info`
--
ALTER TABLE `table_info`
  MODIFY `user_id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_it_studio_sign`
--
ALTER TABLE `table_it_studio_sign`
  MODIFY `sign_id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_it_user_visit`
--
ALTER TABLE `table_it_user_visit`
  MODIFY `visit_id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_message_visit`
--
ALTER TABLE `table_message_visit`
  MODIFY `visit_id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_msg_log`
--
ALTER TABLE `table_msg_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=364;

--
-- 使用表AUTO_INCREMENT `table_services_visit`
--
ALTER TABLE `table_services_visit`
  MODIFY `visit_id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_stu_information`
--
ALTER TABLE `table_stu_information`
  MODIFY `info_id` int(2) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_visit`
--
ALTER TABLE `table_visit`
  MODIFY `visit_id` bigint(50) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `table_wfw_services`
--
ALTER TABLE `table_wfw_services`
  MODIFY `service_id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
