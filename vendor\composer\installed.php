<?php return array(
    'root' => array(
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => 'd0a67629ee499d110342444318fbe567f769e88b',
        'name' => 'topthink/think',
        'dev' => true,
    ),
    'versions' => array(
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.4.0',
            'version' => '6.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => '4dd1e007f22a927ac77da5a3fbb067b42d3bc224',
            'dev_requirement' => false,
        ),
        'topthink/framework' => array(
            'pretty_version' => 'v5.0.25',
            'version' => '5.0.25.0',
            'type' => 'think-framework',
            'install_path' => __DIR__ . '/../topthink/framework',
            'aliases' => array(),
            'reference' => '643c58ed1bd22a2823ce5e95b3b68a5075f9087c',
            'dev_requirement' => false,
        ),
        'topthink/think' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => 'd0a67629ee499d110342444318fbe567f769e88b',
            'dev_requirement' => false,
        ),
        'topthink/think-installer' => array(
            'pretty_version' => 'v1.0.14',
            'version' => '1.0.14.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../topthink/think-installer',
            'aliases' => array(),
            'reference' => 'eae1740ac264a55c06134b6685dfb9f837d004d1',
            'dev_requirement' => false,
        ),
    ),
);
