-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2023-11-28 22:21:04
-- 服务器版本： 5.7.43-log
-- PHP 版本： 7.3.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_msg_log`
--

CREATE TABLE `table_msg_log` (
  `log_id` int(11) NOT NULL,
  `template_number` varchar(255) DEFAULT NULL,
  `message_title` varchar(255) DEFAULT NULL,
  `message_content` char(255) DEFAULT NULL,
  `message_url` char(255) DEFAULT NULL,
  `wx_msgid` varchar(255) DEFAULT NULL,
  `send_time` datetime DEFAULT NULL,
  `send_pid` varchar(255) DEFAULT NULL,
  `send_uid` int(11) DEFAULT NULL,
  `send_user_number` varchar(60) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT NULL,
  `read_time` datetime DEFAULT NULL,
  `url_visit_count` int(11) DEFAULT NULL,
  `soft_delete` tinyint(1) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_msg_log`
--

INSERT INTO `table_msg_log` (`log_id`, `template_number`, `message_title`, `message_content`, `message_url`, `wx_msgid`, `send_time`, `send_pid`, `send_uid`, `send_user_number`, `is_read`, `read_time`, `url_visit_count`, `soft_delete`) VALUES
(359, '323caa799ef44d3b077e5b69f62dc633', '河南农业大学电子邮箱申请', '您的电子邮箱申请已被驳回', 'https://itcservices.henau.edu.cn/#/myApplies', '2958516060761899014', '2023-06-05 10:52:49', '100008', 100005210, '2010120024', 1, NULL, NULL, 0),
(362, '323caa799ef44d3b077e5b69f62dc633', '河南农业大学电子邮箱申请', '您的电子邮箱申请已通过审核', 'https://itcservices.henau.edu.cn/#/myApplies', '2958870458126057474', '2023-06-05 16:44:53', '100008', 100005210, '2010120024', 1, NULL, NULL, 0),
(363, '323caa799ef44d3b077e5b69f62dc633', '河南农业大学网络故障报修申请', '来自信管学院 王建辉（电话：19999999999）的申请', 'https://itcservices.henau.edu.cn/#/myApplies', '2958899239944716290', '2023-06-05 17:13:28', '100008', 100000015, '2104183160', 1, NULL, NULL, 0);

--
-- 转储表的索引
--

--
-- 表的索引 `table_msg_log`
--
ALTER TABLE `table_msg_log`
  ADD PRIMARY KEY (`log_id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_msg_log`
--
ALTER TABLE `table_msg_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=364;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
