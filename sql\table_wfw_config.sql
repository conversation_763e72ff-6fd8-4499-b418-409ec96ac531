-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2024-01-24 13:57:00
-- 服务器版本： 5.7.43-log
-- PHP 版本： 7.3.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `db_wfw`
--

-- --------------------------------------------------------

--
-- 表的结构 `table_wfw_config`
--

CREATE TABLE `table_wfw_config` (
  `module_id` bigint(10) NOT NULL COMMENT '模块id',
  `module_name` varchar(50) DEFAULT NULL,
  `item_id` bigint(20) NOT NULL,
  `item_name` varchar(50) DEFAULT NULL,
  `item_url` varchar(255) DEFAULT NULL,
  `item_content` varchar(500) DEFAULT NULL,
  `item_time` datetime DEFAULT NULL,
  `item_manager_name` varchar(50) DEFAULT NULL,
  `item_manager_number` varchar(50) DEFAULT NULL,
  `item_manager_henau_openid` varchar(50) DEFAULT NULL,
  `is_delete` tinyint(1) DEFAULT '0'
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

--
-- 转存表中的数据 `table_wfw_config`
--

INSERT INTO `table_wfw_config` (`module_id`, `module_name`, `item_id`, `item_name`, `item_url`, `item_content`, `item_time`, `item_manager_name`, `item_manager_number`, `item_manager_henau_openid`, `is_delete`) VALUES
(1, '后端接口基础链接', 1, NULL, 'https://microservices.jsfans.top', NULL, NULL, NULL, NULL, NULL, 0),
(2, '顶部通知栏', 1, '若有校园卡等问题，可在灵感小站进行反馈哦。', NULL, NULL, NULL, NULL, NULL, NULL, 0),
(2, '顶部通知栏', 2, '快去综合服务的失物招领查看、领取防丢码吧。', NULL, NULL, NULL, NULL, NULL, NULL, 0),
(2, '顶部通知栏', 3, '综合服务新增“Chat AI”服务，快去看看吧！', NULL, NULL, NULL, NULL, NULL, NULL, 0),
(3, '公告列表', 1, '微服务平台上线了', 'NoticeWfw', NULL, '2022-05-30 00:00:00', NULL, NULL, NULL, 0),
(3, '公告列表', 2, '灵感小站”上线啦，快投递你的好想法吧!', 'NoticeIdea', NULL, '2022-11-09 00:00:00', NULL, NULL, NULL, 0),
(3, '公告列表', 3, '追寻你的偏好：深色模式适配', 'NoticeDark', NULL, '2022-12-12 00:00:00', NULL, NULL, NULL, 0),
(3, '公告列表', 4, '引入微信小程序并对服务进行分类', 'NoticeClassification', NULL, '2023-03-02 00:00:00', NULL, NULL, NULL, 0),
(3, '公告列表', 5, '不了解微服务？快来看看吧', 'NoticeUpdatePlatform', NULL, '2023-09-18 00:00:00', NULL, NULL, NULL, 0),
(3, '公告列表', 6, '微服务升级啦，全新平台等你来体验！', 'NoticeRebuild', NULL, '2023-12-02 00:00:00', NULL, NULL, NULL, 0),
(4, '校园服务电话', 1, '信息化办公室', NULL, '0371-60675051（校园卡龙子湖）&0371-60675050（校园卡文化路）&0371-56990002（邮箱）&0371-56552505（OA）&0371-63558092（VPN）&0371-56990008（网络（除图书馆区域））', NULL, NULL, NULL, NULL, 0),
(4, '校园服务电话', 2, '保卫处', NULL, '0371-56990110（龙子湖）&0371-63558110（文化路）', NULL, NULL, NULL, NULL, 0),
(4, '校园服务电话', 3, '后勤处', NULL, '0371-56990206（龙子湖）&0371-63558558（文化路）&0374-7387856（许昌）', NULL, NULL, NULL, NULL, 0),
(4, '校园服务电话', 4, '校医院', NULL, '0371-56990120（龙子湖）&0371-63558120（文化路）', NULL, NULL, NULL, NULL, 0),
(4, '校园服务电话', 5, '教务处', NULL, '0371-56990353', NULL, NULL, NULL, NULL, 0),
(4, '校园服务电话', 6, '图书馆', NULL, '0371-56552785（龙子湖）&0371-63558080（文化路）&0371-56552535（网络）', NULL, NULL, NULL, NULL, 0),
(4, '校园服务电话', 7, '档案馆', NULL, '0371-56552792', NULL, NULL, NULL, NULL, 0),
(5, '技术支持弹出链接', 1, '河南农业大学IT工作室官方网站', 'https://itstudio.henau.edu.cn/', NULL, NULL, NULL, NULL, NULL, 0),
(5, '技术支持弹出链接', 2, '河南农业大学IT工作室证书溯源系统', 'https://itstudio.henau.edu.cn/cert/public/item/#/certificateSearch', NULL, NULL, NULL, NULL, NULL, 0),
(6, '版本号', 1, '1.3.2', NULL, NULL, NULL, NULL, NULL, NULL, 0),
(7, '灵感小站建议类型', 1, '校园卡相关', NULL, NULL, NULL, '测试A', '123456', NULL, 0),
(7, '灵感小站建议类型', 2, '校园网相关', NULL, NULL, NULL, '测试B', '123456', NULL, 0),
(7, '灵感小站建议类型', 3, '多媒体教室相关', NULL, NULL, NULL, '测试C', '123456', NULL, 0),
(7, '灵感小站建议类型', 4, 'Chat AI相关', NULL, NULL, NULL, '测试D', '123456', NULL, 0);

--
-- 转储表的索引
--

--
-- 表的索引 `table_wfw_config`
--
ALTER TABLE `table_wfw_config`
  ADD PRIMARY KEY (`module_id`,`item_id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `table_wfw_config`
--
ALTER TABLE `table_wfw_config`
  MODIFY `module_id` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '模块id', AUTO_INCREMENT=8;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
