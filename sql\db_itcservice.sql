/*
 Navicat Premium Data Transfer

 Source Server         : db_itcservice
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : localhost:3306
 Source Schema         : db_itcservice

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 21/09/2023 17:12:27
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for table_app
-- ----------------------------
DROP TABLE IF EXISTS `table_app`;
CREATE TABLE `table_app`  (
  `app_id` int(10) NOT NULL AUTO_INCREMENT,
  `app_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `app_approval_list` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `app_apply_num` bigint(20) NULL DEFAULT 0,
  `app_apply_user_type` int(10) NULL DEFAULT NULL,
  `app_apply_pass_num` bigint(20) NULL DEFAULT 0,
  `soft_delete` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`app_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of table_app
-- ----------------------------
INSERT INTO `table_app` VALUES (1, '河南农业大学电子邮箱申请', '[[\"2010120024\"]]', 49, 7, 22, 0);
INSERT INTO `table_app` VALUES (2, '河南农业大学VPN服务申请', '[[\"2010120024\"]]', 51, 6, 20, 0);
INSERT INTO `table_app` VALUES (3, '河南农业大学IP地址申请 ', '[[\"2010120024\"]]', 2, 4, 0, 0);
INSERT INTO `table_app` VALUES (4, '河南农业大学服务器申请', '[[\"2010120024\"]]', 4, 4, 1, 0);
INSERT INTO `table_app` VALUES (5, '河南农业大学域名申请', '[[\"2010120024\"]]', 4, 4, 1, 0);
INSERT INTO `table_app` VALUES (6, '河南农业大学无线上网申请', '[[\"2010120024\"]]', 0, 7, 0, 0);
INSERT INTO `table_app` VALUES (7, '河南农业大学网络故障报修申请', '[[\"2010120024\"]]', 5, 6, 1, 0);
INSERT INTO `table_app` VALUES (8, '河南农业大学多媒体教室申请', '[[\"2010120024\"]]', 5, 7, 1, 0);

-- ----------------------------
-- Table structure for table_apply
-- ----------------------------
DROP TABLE IF EXISTS `table_apply`;
CREATE TABLE `table_apply`  (
  `apply_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请序号',
  `apply_app_id` int(10) NULL DEFAULT NULL COMMENT '申请app序号',
  `apply_app_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请app名称',
  `app_approval_list` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '申请审批人列表',
  `approval_stage` tinyint(4) NULL DEFAULT NULL COMMENT '申请审批状态',
  `apply_user_henau_openid` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `apply_user_name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人姓名',
  `apply_user_number` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人学工号',
  `apply_user_section` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人所在学院/部门',
  `apply_user_email` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人邮箱',
  `apply_phone_number` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人手机号',
  `apply_reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '申请原因',
  `apply_attachment` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '附件访问链接',
  `apply_status` tinyint(1) NULL DEFAULT NULL COMMENT '申请状态',
  `apply_time` datetime NULL DEFAULT NULL COMMENT '申请时间',
  `remark` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '老师同意或驳回愿意',
  `approval_time` datetime NULL DEFAULT NULL,
  `approval_user_name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批人姓名',
  `approval_user_number` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批人姓名',
  `approval_user_section` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批人所在部门',
  `apply_user_ua_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '申请人所使用ua',
  `apply_user_ip_info` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请人所在ip',
  `apply_evidence_file_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请证明文件名称',
  `apply_evidence_save_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请证明文件存储位置',
  `is_delete` tinyint(1) NULL DEFAULT 0 COMMENT '软删除',
  PRIMARY KEY (`apply_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 32 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of table_apply
-- ----------------------------

-- ----------------------------
-- Table structure for table_request_rate_limit
-- ----------------------------
DROP TABLE IF EXISTS `table_request_rate_limit`;
CREATE TABLE `table_request_rate_limit`  (
  `id` bigint(100) NOT NULL AUTO_INCREMENT,
  `time_stamp` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `request_time` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `real_ip` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `request_num` int(3) NOT NULL,
  `request_api` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `limit_time` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `request_api_origin` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of table_request_rate_limit
-- ----------------------------

-- ----------------------------
-- Table structure for table_user
-- ----------------------------
DROP TABLE IF EXISTS `table_user`;
CREATE TABLE `table_user`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `number` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `henau_openid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `email` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `role` tinyint(1) NULL DEFAULT NULL,
  `department` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `role_mask` int(10) NULL DEFAULT NULL,
  `soft_delete` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of table_user
-- ----------------------------

-- ----------------------------
-- Table structure for table_visit
-- ----------------------------
DROP TABLE IF EXISTS `table_visit`;
CREATE TABLE `table_visit`  (
  `visit_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `visit_time` datetime NULL DEFAULT NULL,
  `visit_app_id` int(10) NULL DEFAULT NULL,
  `visit_app_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `visit_url` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL,
  `visit_ip` varchar(60) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `visit_ua` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL,
  `visiter_henau_openid` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `visiter_name` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  `visiter_section` varchar(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`visit_id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 303 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of table_visit
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
