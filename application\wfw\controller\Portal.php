<?php

namespace app\wfw\controller;

use think\Request;
use app\wfw\model\Visit;
use app\wfw\model\NoticeView;
use app\wfw\model\NoticeCreate;
use app\wfw\model\IdeasUser;
use app\wfw\model\ServicesVisit;
use app\wfw\model\WfwServices;
use app\wfw\model\WfwConfig; 
use app\wfw\model\MsgLog;
use app\wfw\model\MessageVisit;
use app\wfw\model\HenauNotice;
use org\wechat\jssdk;

class Portal extends Base
{
    // 统一门户获取用户信息
    public function PortalGetUserInfo() {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // 从 GET 请求中获取 code 参数
        $code = input('get.code');
        
        // 对于未传递 code 参数的请求进行拦截
        if (!$code) {
            Error('非法请求！');
        }

        // 通过传入的 code 参数，调用 httpGet 函数获取 access_token
        $data = json_decode(httpGet(config('GET_ACCESS_TOKEN_API') . '?appid=' . config('AppID') . '&secret=' . config('AppSecret') . '&code=' . $code . '&grant_type=authorization_code'));

        // 判断是否成功获取 access_token
        if (!isset($data->status) || $data->status != 'success') {
            Error($data->data);
        }

        // 从对象中获取 access_token 和 henau_openid
        $data = $data->data;
        $access_token = $data->access_token;
        $henau_openid = $data->henau_openid;

        // 通过 access_token 和 henau_openid 换取用户身份信息
        $user_info = json_decode(httpGet(config('GET_USER_INFO_API') . '?access_token=' . $access_token . '&henau_openid=' . $henau_openid));

        // 如果请求返回为空或请求状态不为 'success' 则抛出异常
        if (!isset($user_info->status) || $user_info->status != 'success') {
            Error($data->data);
        }

        // 从获取的用户身份信息中获取用户的学号、姓名、学院
        $user_data = $user_info->data;
        $user_number = $user_data->user_number;
        $user_name = $user_data->user_name;
        $user_section = $user_data->user_section;
        $user_status = $user_data->user_status;

        // 保存访问信息到用户访问表
        $userVisit = new Visit([
            'visiter_user_name' => $user_name,
            'visiter_user_number' => $user_number,
            'visiter_user_section' => $user_section,
            'visiter_user_henau_openid' => $henau_openid,
            'visit_ip' => $trueIp,
            'visit_time' => date("Y-m-d H:i:s", time()),
            'visit_ua' => $ua,
            'visit_type' => 'portal',
            'visiter_user_status' => $user_status,
        ]);
        $vis_count_num = $userVisit->count();
        $response = $userVisit->save();
        if (!$response)
            Error('请求失败，请稍后重试！');

        $res['user_name'] = $user_name;
        $res['user_number'] = $user_number;
        $res['user_section'] = $user_section;
        $res['user_henau_openid'] = $henau_openid;
        $res['user_status'] = $user_status;

        // 创建 JWT Token
        $token = $this->CreatJwtToken($res);

        // 判断是否局域网，开发环境下使用第二条语句，生产环境下使用第一条语句
        $is_henau_lan = !isPublicIP($trueIp);
        // $is_henau_lan = isPublicIP($trueIp);

        // 微服务访问量
        $vis_count = $vis_count_num > 10000 ? ($vis_count_num - $vis_count_num % 100) / 10000 . "万" : $vis_count_num;

        // 未读消息数量，此处返回未读消息数量
        $unread_msg_count = MsgLog::where('send_user_number', $user_number)->where('is_read', null)->count();

        // 返回灵感小站中的身份：1代表建议回复人，0代表普通用户
        $ideas_user = IdeasUser::get(['user_number' => $user_number]);
        $user_ideas_role = 0;
        if ($ideas_user) {
            $user_ideas_role = 1;
        }

        // 获取后端接口根链接
        $base_url = WfwConfig::where('module_id', 1)->where('item_id', 1)->where('is_delete', 0)->select();

        // 微信开放标签
        // $config = config('wechat');
        // $jssdkObj = new Jssdk1($config['appId'], $config['appsecret'],$config['getWebUrl']);
        // $res = $jssdkObj->getSignPackage();
        // $appId      = $res['appId'];
        // $timestamp  = $res['timestamp'];
        // $nonceStr   = $res['nonceStr'];
        // $signature  = $res['signature'];

        // 返回成功响应
        $res_data = [
            'token' => $token,
            'user_name' => $user_name,
            'user_number' => $user_number,
            'user_section' => $user_section,
            'user_status' => $user_status,
            'is_henau_lan' => $is_henau_lan,
            'wfw_visit_count' => $vis_count,
            'unread_message_count' => $unread_msg_count,
            'user_ideas_role' => $user_ideas_role,
            // 'services_list' => $servicesList,
            'base_url' => $base_url,
            // 'wx_config' => [
            //     'appId'      =>  $appId,
            //     'timestamp'  =>  $timestamp,
            //     'nonceStr'   =>  $nonceStr,
            //     'signature'  =>  $signature,
            // ]
        ];

        PostResSuccess($res_data);
    }
    // 点击服务统计访问信息接口
    public function PortalCollectServiceVisitCount()
    {
        // 判断是否是 POST 请求
        if (!Request::instance()->isPost()) {
            Error('非法请求！');
        }

        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');
        $check_res =  $this->CheckJwtToken($jwt_token);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        // 对请求频次进行限制
        $data = WfwRequestLimit(config('SAVEDATA_REQUEST_LIMIT_TIME'), config('SAVEDATA_REQUEST_LIMIT_NUM'));
        // 返回请求频次超限的提示信息
        if ($data['code'] == 1) {
            Error($data['msg']);
        }

        // 从jwt_token中获取学号等信息
        $user_info = $check_res['data']->user_info;

        // 在请求、数据库中取出服务对应信息并判断
        $service_id = input('post.service_id');
        if(!CheckId($service_id)){
            Error('参数错误！');
        }

        // 判断数据库是否存在对应数据
        $service_info = WfwServices::get(['service_id' => $service_id]);
        if (empty($service_info)) {
            Error('参数错误！');
        }

        // 保存访问数据
        $userVisit = new ServicesVisit([
            'visit_time' => date('Y-m-d H:i:s'),
            'visit_ua' => $ua,
            'visit_ip' => $trueIp,
            'visit_service_id' => $service_id,
            'visit_service_name' => $service_info->service_name,
            'visit_type' => 'portal',
            'visitor_name' => $user_info->user_name,
            'visitor_number' => $user_info->user_number,
            'visitor_section' => $user_info->user_section,
            'visitor_henau_openid' => $user_info->user_henau_openid
        ]);
        $response = $userVisit->save();
        if (!$response)
            ErrorLog($user_info->user_section.$user_info->user_name.'(学号：'.$user_info->user_number.')'.'在'.date('Y-m-d H:i:s', time()).'访问'.$service_info->service_name.'失败！');

        // 添加服务访问量
        WfwServices::where('service_id', $service_id)->setInc('service_visit_count');

        Success("请求成功！");
    }
    // 转化用户身份
    public function mapUserStatusToServiceVisitUser($userStatus) {
        $type = 0;
        // if ($userStatus === 0) {
        //     $type = 1; // 本科生
        // } else if ($userStatus === 1) {
        //     $type = 2; // 研究生
        // } else if ($userStatus === 2 || $userStatus === 3) {
        //     $type = 3; // 教职工
        // } else if ($userStatus === 4) {
        //     $type = 4; // 校友
        // } else if ($userStatus === 5) {
        //     $type = 5; // 现代农业联合研究生院研究生
        // } else {
        //     $type = 0; // 未知
        // }
        // return $type;
        if ($userStatus === 0) {
            $type = 1; // 本科生
        } else if ($userStatus === 1) {
            $type = 2; // 研究生
        } else if ($userStatus === 2) {
            $type = 3; // 在校教职工
        } else if ($userStatus === 3) {
            $type = 4; // 校后勤/外聘人员
        } else if ($userStatus === 4) {
            $type = 5; // 校友
        } else if ($userStatus === 5) {
            $type = 6; // 现代农业联合研究生院研究生
        } else {
            $type = 0; // 未知
        }
        return $type;
    }

    // 统一门户获取服务列表
    public function PortalGetServicesList() {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }
        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');

        $check_res =  $this->CheckJwtToken($jwt_token);

        // Error($check_res);
        // PostResSuccess($check_res);
        if ($check_res['status'] === 0)
            Error($check_res['msg']);
        
        // 通过传入的 user_status 参数，获取对应角色的服务列表
        $user_status = $check_res['data']->user_info->user_status;

        $serviceVisitUser = $this->mapUserStatusToServiceVisitUser($user_status);
        
        $servicesList = WfwServices::field('service_id, service_module_id,service_module_name,service_type,  service_name,service_description,service_url,service_icon,is_henau_lan_service,open_in_pc,hot_service,service_visit_user,service_wxapp_username, is_wfw_service,is_new_service,service_order,hot_service_order')
                                    ->where('is_delete', 0)->where('is_portal_service', 1)
                                    ->where('service_visit_user',   'like', "%{$serviceVisitUser}%")
                                    ->select();
        // 对返回的服务模块进行排序
        $customOrder = [1, 2, 3, 4, 6, 7, 8, 9];
        usort($servicesList, function($a, $b) use ($customOrder) {
            // 第一优先级：模块排序
            $aIndex = array_search($a['service_module_id'], $customOrder);
            $bIndex = array_search($b['service_module_id'], $customOrder);
        
            // 处理未定义的模块（放到最后）
            $aIndex = ($aIndex !== false) ? $aIndex : PHP_INT_MAX;
            $bIndex = ($bIndex !== false) ? $bIndex : PHP_INT_MAX;
        
            if ($aIndex !== $bIndex) {
                return ($aIndex < $bIndex) ? -1 : 1;
            }
        
            // 第二优先级：service_order
            if ($a['service_order'] != $b['service_order']) {
                return ($a['service_order'] > $b['service_order']) ? -1 : 1;
            }
        
            // 第三优先级：service_id
            return ($a['service_id'] < $b['service_id']) ? -1 : 1;
        });
        // 返回服务列表
        PostResSuccess($servicesList);
    }

    // 统一门户获取热门服务列表
    public function PortalGetHotServicesList(){
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }
        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');

        $check_res =  $this->CheckJwtToken($jwt_token);

        if ($check_res['status'] === 0)
            Error($check_res['msg']);
        
        // 通过传入的 user_status 参数，获取对应角色的服务列表
        $user_status = $check_res['data']->user_info->user_status;

        $serviceVisitUser = $this->mapUserStatusToServiceVisitUser($user_status);
        
        $servicesList = WfwServices::field('service_id, service_module_id,service_module_name,service_type,  service_name,service_description,service_url,service_icon,is_henau_lan_service,open_in_pc,hot_service,service_visit_user,service_wxapp_username, is_wfw_service,is_new_service')
                                    ->where('is_delete', 0)->where('is_portal_service', 1)
                                    ->where('hot_service', 'like', "%{$serviceVisitUser}%")
                                    ->where('service_visit_user', 'like', "%{$serviceVisitUser}%")
                                    ->select();

        // 返回服务列表
        PostResSuccess($servicesList);
    }

    // 统一门户失物招领小组件
    public function PortalGetMySwzlCount() {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }
        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');

        $check_res =  $this->CheckJwtToken($jwt_token);

        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        $user_name = $check_res['data']->user_info->user_name;
        $user_number = $check_res['data']->user_info->user_number;

        $body = [
            'auth_secret' => config('GETMYSWZLCOUNT_KEY'),
        ];
        // 准备参数
        $params = [
            'user_name' => $user_name,
            'user_number' => $user_number,
        ];
        $urlWithParams = 'https://swzl.henau.edu.cn/swzl/open_auth/getmyswzlcount'. '?'. http_build_query($params);
        $result = HttpPost($urlWithParams, $body);
        PostResSuccess($result);
    }

    // 统一门户获取通知公告信息
    public function PortalGetHenauNotice() {
        // 判断是否是 GET 请求
        if (!Request::instance()->isGet()) {
            Error('非法请求！');
        }
        // 获取用户的真实 IP 和 User-Agent 信息
        $trueIp = GetReverseProxyIP();
        $ua = Request::instance()->header('user-agent');

        // 如果获取的 IP 为 'null' 或者 User-Agent 信息非法
        if ($trueIp === 'null' || !isValidUA($ua)) {
            Error('非法请求！');
        }

        // JWT 用户验证模块
        $jwt_token = Request::instance()->header('Authorization');

        $check_res =  $this->CheckJwtToken($jwt_token);

        if ($check_res['status'] === 0)
            Error($check_res['msg']);

        $result = HenauNotice::field('id, title, date, crawl_time, url, type')
                            ->order('date', 'desc')
                            ->limit(2)
                            ->select();
        $format_result = [];
        foreach ($result as $item) {
            $format_result[] = [
                'title' => $item['title'],
                'date' => substr($item['date'], 0, 10),
                'url' => $item['url'],
                'type' => $item['type']
            ];
        }
        PostResSuccess($format_result);
    }

}
